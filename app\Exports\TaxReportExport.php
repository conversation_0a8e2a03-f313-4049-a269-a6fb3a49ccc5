<?php

namespace App\Exports;

use App\Libraries\AllSettingFormat;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class TaxReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {
        $taxData =  Order::join('branches', 'orders.branch_id', '=', 'branches.id')
        ->select('orders.id', 'orders.date', 'orders.order_type', 'orders.total_tax as tax', 'branches.name', 'orders.total', 'orders.invoice_id as invoice_id')
        ->orderBy('orders.invoice_id', 'desc');

        $data = $taxData->get();
        foreach ($data as $rowData) {
            if ($rowData->order_type == 'sales') {
                $rowData->order_type = Lang::get('lang.sales');
            } else {
                $rowData->order_type = Lang::get('lang.receiving');
            }
        }
        return $taxData;
    }

    public function map($reportRow): array
    {       
        $allSettingFormat = new AllSettingFormat;
        return 
            [
                $reportRow->invoice_id,
                Carbon::createFromFormat('Y-m-d', $reportRow->date)->format('d/m/Y'),
                $reportRow->order_type,
                $reportRow->name,
                 $allSettingFormat->getCurrencySeparator(number_format((float)$reportRow->total, 2, '.', '')),
                 $allSettingFormat->getCurrencySeparator(number_format((float)$reportRow->tax, 2, '.', '')),
            ];                    
    }

    public function headings(): array
    {
        return [
           "Invoice ID", "Date", "Type", "Branch", "Total", "Tax"
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->getStyle('A1:F1')->applyFromArray([
                        'font' =>[
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' =>[
                                'color' => ['argb' =>'FFFF0000']
                            ],
                        ]
                    ],
                );
                
                
                $query_result = $this->query()->get();
                $rows_count = count($query_result);
                    
                $tax  = $query_result->sum('tax');  
                $total          = $query_result->sum('total');
                $rows_count = $rows_count + 3;

                $allSettingFormat = new AllSettingFormat;   
                $event->sheet->setCellValue('A'.$rows_count, 'Grand Total'); 
                $event->sheet->setCellValue('E'.$rows_count,  $allSettingFormat->getCurrencySeparator($total));
                $event->sheet->setCellValue('F'.$rows_count,  $allSettingFormat->getCurrencySeparator($tax));
                
            },
        ];
    }
    
}
