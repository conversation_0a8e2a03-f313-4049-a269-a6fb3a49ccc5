<?php

namespace App\Exports;

use App\Libraries\AllSettingFormat;
use App\Models\Payments;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Lang;

class PaymentsSummaryExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {
        return Payments::leftJoin('payment_types', 'payment_types.id', '=', 'payments.payment_method')
            ->leftJoin('orders', 'payments.order_id', '=', 'orders.id')
            ->where('orders.status', '=', 'done')
            ->select(
                'payments.date',
                'orders.id',
                DB::raw('sum(payments.paid) as total'),
                DB::raw('sum(payments.exchange) as totalChange'),
                DB::raw('GROUP_CONCAT(DISTINCT(payment_types.name)) as method'),
                DB::raw('GROUP_CONCAT(DISTINCT(orders.order_type)) as type')
            )
            ->groupBy('payments.date')
            ->orderBy('payments.date', 'DESC');
    }

    public function map($reportRow): array
    {
        $allSettingFormat = new AllSettingFormat;

        $typeArray = explode(',', $reportRow->type);
        for ($i = 0; $i < count($typeArray); $i++) {
            if ($typeArray[$i] == 'sales') {
                $typeArray[$i] = Lang::get('lang.sales');
            } else {
                $typeArray[$i] = Lang::get('lang.receiving');
            }
        }
        $type = implode(',', $typeArray);

        return
            [
                Carbon::createFromFormat('Y-m-d', $reportRow->date)->format('d/m/Y'),
                $type,
                $reportRow->method,
                $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->total, 2, '.', '')),
            ];
    }

    public function headings(): array
    {
        return [
            "Date",
            "Type",
            "Payment Method",
            "Total",
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:D1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );

                $allSettingFormat = new AllSettingFormat;

                $query_result = $this->query()->get();
                $rows_count = count($query_result);

                $total = $query_result->sum('total');
                $rows_count = $rows_count + 2;

                $event->sheet->setCellValue('A' . $rows_count, 'Grand Total');
                $event->sheet->setCellValue('D' . $rows_count, $allSettingFormat->getCurrencySeparator($total));

            },
        ];
    }

}
