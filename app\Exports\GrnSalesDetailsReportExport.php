<?php

namespace App\Exports;

use App\Models\OrderItems;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class GrnSalesDetailsReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;
    public $filterData;
    public $searchValue;

    function __construct($filterData, $searchValue){
        $this->filterData = $filterData; 
        $this->searchValue = $searchValue;
    }

    public function query()
    {
        $_searchValue = $this->searchValue;
        $_filterData  = $this->filterData;


        $query = OrderItems::query()->leftjoin('products', 'order_items.product_id', '=', 'products.id')
            ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
            ->leftJoin('product_variants', 'order_items.variant_id', '=', 'product_variants.id')
            ->leftJoin('product_units', 'products.unit_id', '=', 'product_units.id')
            ->leftJoin('suppliers', 'suppliers.id', '=', 'orders.supplier_id')
            ->select(
                'products.brand_id',
                'products.category_id',
                'products.group_id',
                'order_items.sub_total',
                'orders.invoice_id',
                'orders.grn_invoice',
                'order_items.type',
                'orders.date',
                'product_units.name',
                DB::raw('(concat(title,if(variant_title="default_variant"," ",concat("(",product_variants.variant_title,")")))) as title'),
                DB::raw('abs(ROUND(order_items.quantity,2)) as quantity'),
                'order_items.discount',          
                DB::raw("DATE_FORMAT(orders.grn_invoice_ref_date, '%d/%m/%Y') AS grn_invoice_ref_date"), 
                DB::raw("CONCAT(suppliers.first_name,' ',suppliers.last_name)  AS supplier_name"),
                'orders.grn_invoice_number_ref'
            )
            ->where('orders.order_type', '=', 'receiving')
            ->where('orders.status', '=', 'done')
            ->whereNotNull('orders.grn_invoice');

        if (!empty($_filterData)) {
            foreach ($_filterData as $singleFilter) {
                if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                    $query->where('orders.date', '>=', $singleFilter['value'][0]['start'])
                        ->where('orders.date', '<=', $singleFilter['value'][0]['end']);
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "brands") {
                    $query->where('products.brand_id', $singleFilter['value']);
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "sales_type") {
                    if ($singleFilter['value'] == 'returns') {
                        $query->where('orders.total', '<', 0);
                    } else if ($singleFilter['value'] == 'internal-transfer') {
                        $query->where('orders.type', $singleFilter['value']);
                    } else $query->where('orders.type', $singleFilter['value']);
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "categories") {

                    $query->where('products.category_id', $singleFilter['value']);
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "groups") {

                    $query->where('products.group_id', $singleFilter['value']);
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "allInvoiceId") {

                    $query->where('orders.invoice_id', $singleFilter['value']);
                }
            }
        }
                        
        if (!empty($_searchValue)) {
            $query->where(function ($query) use ($_searchValue) {
                $query->Where('orders.invoice_id', 'LIKE', '%' . $_searchValue . '%')
                    ->orWhere('orders.grn_invoice', 'LIKE', '%' . $_searchValue . '%')
                    ->orWhere('order_items.price', 'LIKE', '%' . $_searchValue . '%')
                    ->orWhere('title', 'LIKE', '%' . $_searchValue . '%');
            });
        }

        $query->having('quantity', '>', 0);
        
        return $query;

    }

    public function map($reportRow): array
    {       
        return 
            [
                $reportRow->grn_invoice,
                $reportRow->invoice_id,
                date('d/m/Y', strtotime($reportRow->date)),
                $reportRow->supplier_name,
                $reportRow->grn_invoice_number_ref,
                $reportRow->grn_invoice_ref_date,
                $reportRow->title,
                number_format((float)$reportRow->quantity, 2, '.', ''),
                $reportRow->name
            ];
    }

    public function headings(): array
    {
        return [
           "GRN Invoice ID", "PO Invoice ID", "Date","Supplier Name","Supplier Invocie ID","Supplier Invocie Date","Items", "Quantity", "Unit"

        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->getStyle('A1:H1')->applyFromArray([
                        'font' =>[
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' =>[
                                'color' => ['argb' =>'FFFF0000']
                            ],
                        ]
                    ],
                );
                
                
                $query_result = $this->query()->get();
                $rows_count = count($query_result);
                    
                $quantity  = $query_result->sum('quantity');   
                $rows_count = $rows_count + 3;
                                
                $event->sheet->setCellValue('A'.$rows_count, 'Grand Total'); 
                $event->sheet->setCellValue('D'.$rows_count, number_format($quantity)); 
                
            },
        ];
    }
    
}
