<?php

namespace App\Models;

class ProductCourse extends BaseModel
{
    protected $table = 'product_courses';
    protected $fillable = ['course_name', 'combo_id_fk'];
    
    
    public static function getCourseList()
    {
        $data = ProductCourse::select('course_name as name', 'id')->orderBy('id', 'asc')->get();
        return $data;
    }
    
    public static function getCourseData($request)
    {
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $rowLimit = $request->rowLimit;
        $offset = $request->rowOffset;
        $requestType = $request->reqType;

        if (empty($requestType)) {
            $data = ProductCourse::orderBy($columnName, $request->columnSortedBy)->take($rowLimit)->skip($offset)->get();

        }else{
            $data = ProductCourse::orderBy($columnName, $request->columnSortedBy)->get();
        }
        $count = ProductCourse::count();

        return ['data' => $data, 'count' => $count];
    }
    public static function idOfExisted($fields, $column, $value)
    {
        return ProductCourse::select($fields)->where($column[0], $value[0])->orWhere($column[1], $value[1])->first();
    }

    public static function isCourseExits($name)
    {
        return ProductCourse::where('course_name', $name)->count();
    }

    public static function getCourseId($name)
    {
        $productCourse =  ProductCourse::where('course_name', $name)->first();

        return isset($productCourse->id) ? $productCourse->id : '';
    }

    
}
