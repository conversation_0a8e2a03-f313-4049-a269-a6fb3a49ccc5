<?php

namespace App\Imports;

use App\Models\ProductVariant;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;

class ProductVariantsImport implements ToCollection
{
    public function collection(Collection $rows)
    {
        foreach ($rows as $row) 
        {
            // Assuming the SKU is in the first column and the purchase_price in the second
            $sku = $row[0];
            $purchasePrice = $row[1];

            // Update the purchase_price in the product_variants table
            ProductVariant::where('sku', $sku)->update(['purchase_price' => $purchasePrice]);
        }
    }
}
