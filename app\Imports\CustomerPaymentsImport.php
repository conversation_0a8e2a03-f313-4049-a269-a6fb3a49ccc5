<?php

namespace App\Imports;

use App\Models\Customer;
use App\Models\CustomerPayment;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Facades\Log;

class CustomerPaymentsImport implements ToModel, WithHeadingRow
{
    public function model(array $row)
    {
        // Validate the required fields
        if (empty($row['reg_no'])) {
            throw new \Exception("Validation error: 'Roll No " . json_encode($row));
        }
        if($row['reg_no']){
            $customer = Customer::where('roll_no', 'like', $row['reg_no'])->first();

            if($customer)
            {
                    $data = [
                        'customer_id' => intval($customer->id),
                        'fees' => $row['total_amount'],
                        'balance' => $row['total_pending_amount'],
                        'paid' => $row['total_paid_amount'],
                    ];
        
                    CustomerPayment::updateOrInsert(
                        ['customer_id' => $customer->id], // Condition to check for existing record
                        $data // Data to update or insert
                    );

            }
            
                
        }else{
            throw new \Exception("error occured while insertion due to Rolll NO is empty");
        }



    }
}
