<?php

namespace App\Imports;

use App\Models\Customer;
use App\Models\Gender;
use App\Models\ProductCourse;
use App\Models\SemesterYears;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Facades\Validator;

class CustomersImport implements ToModel, WithHeadingRow
{
    public $errors = [];
    public $insertedCount = 0;
    public $updatedCount = 0;

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     * @throws \Exception
     */
    public function model(array $row)
    {
        $gender_id = null;
        $course_id = null;
        $semester_year_id = null;
        set_time_limit(5000);
        // Check if customer with the same roll_no already exists
        $existingCustomer = Customer::where('roll_no', $row['roll_no'])->first();

        // Validate required fields
        $validator = Validator::make($row, [
            'first_name' => 'required',
            'last_name'  => 'required',
        ]);

        if ($validator->fails()) {
            $this->errors[] = "Validation error: first_name and last_name are required. Row data: " . json_encode($row);
            return null;
        }

        try {
            if (!empty($row['gender'])) {
                $gender = Gender::where('name', $row['gender'])->first();
                if ($gender) {
                    $gender_id = $gender->id;
                } else {
                    $this->errors[] = "Gender not found: " . $row['gender'];
                    return null;
                }
            }
        } catch (\Exception $e) {
            $this->errors[] = "Error fetching gender: " . $e->getMessage();
            return null;
        }

        try {
            if (!empty($row['course'])) {
                $course = ProductCourse::where('course_name', $row['course'])->first();
                if ($course) {
                    $course_id = $course->id;
                } else {
                    $this->errors[] = "Course not found: " . $row['course'];
                    return null;
                }
            }
        } catch (\Exception $e) {
            $this->errors[] = "Error fetching course: " . $e->getMessage();
            return null;
        }

        try {
            if (!empty($row['semester_year'])) {
                $semester_year = SemesterYears::where('title', $row['semester_year'])->first();
                if ($semester_year) {
                    $semester_year_id = $semester_year->id;
                } else {
                    $this->errors[] = "Semester year not found: " . $row['semester_year'];
                    return null;
                }
            }
        } catch (\Exception $e) {
            $this->errors[] = "Error fetching semester year: " . $e->getMessage();
            return null;
        }

        try {
            if ($existingCustomer) {
                // Update existing customer
                if($existingCustomer->course_id == null || $existingCustomer->course_id == ""){
                    $existingCustomer->course_id = $course_id;
                }
                if($existingCustomer->company == null || $existingCustomer->company == ''){
                    $existingCustomer->company = $row['school_or_college'];
                }
                if($row['semester_year'] != ""){
                    $existingCustomer->semester_year = $semester_year_id;
                }

                $existingCustomer->save();
                $this->updatedCount++;
                return $existingCustomer;
            } else {
                // Create new customer
                $customer = new Customer([
                    'first_name'      => $row['first_name'],
                    'last_name'       => $row['last_name'],
                    'email'           => $row['email'],
                    'phone_number'    => $row['phone_number'],
                    'address'         => $row['address'],
                    'gender_id'       => $gender_id,
                    'aadhr_card_no'   => $row['aadhr_card_no'],
                    'roll_no'         => $row['roll_no'],
                    'company'         => $row['school_or_college'],
                    'customer_group'  => $row['customer_group'],
                    'course_id'       => $course_id,
                    'semester_year'   => $semester_year_id,
                ]);
                $customer->save();
                $this->insertedCount++;
                return $customer;
            }
        } catch (\Exception $e) {
            $this->errors[] = "Error creating or updating customer: " . $e->getMessage();
            return null;
        }
    }

    public function getErrors()
    {
        return $this->errors;
    }

    public function getInsertedCount()
    {
        return $this->insertedCount;
    }
    public function getUpdatedCount()
    {
        return $this->updatedCount;
    }
}
