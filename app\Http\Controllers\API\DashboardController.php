<?php

namespace App\Http\Controllers\API;

use App\Models\CashRegisterLog;
use App\Models\Order;
use App\Models\Product;
use App\Http\Controllers\Controller;
use function GuzzleHttp\Promise\all;
use Carbon\Carbon;
use App\Libraries\AllSettingFormat;
use App\Models\OrderItems;
use App\Models\Payments;


class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function getAllData($branchId)
    {
        $this->setBranchId($branchId);

        $basicData = $this->getBasicData($branchId);
        $barChartData = $this->barChartData($branchId);
        $lineChartData = $this->lineChartData($branchId);

        return ['basicData' => $basicData, 'barChartData' => $barChartData, 'lineChartData' => $lineChartData];
    }

    public function getBasicData($branchId)
    {
        $today = Carbon::today()->toDateString();
        $date = Carbon::today()->subDays(30);
        $date = date('Y-m-d', strtotime($date));

        $data = array();

        $data['todaySales'] = Order::todaysSale($today, $branchId);

        $data['monthlySale'] = Order::monthlySold($date, $branchId);

        $data['totalSale'] = Order::totalSold($branchId);

        $data['totalReturn'] = 0;

        $data['todayProfit'] = Order::todayProfit($today, $branchId);

        $data['monthlyProfit'] = Order::monthlyProfit($date, $branchId);

        $data['totalProfit'] = Order::totalProfit($branchId);

        return $data;
    }

    public function barChartData($branchId)
    {
        $year = date("Y");

        $monthlySale = OrderItems::monthlySale($year, $branchId);
        $monthlyArraySale = $this->manipulateBarChart($monthlySale, 'sales');
        $monthlyReceive = OrderItems::monthlyReceive($year, $branchId);
        $monthlyArrayReceive = $this->manipulateBarChart($monthlyReceive, 'receive');

        $monthlyProfit = OrderItems::monthlyProfit($year, $branchId);
        $monthlyArrayProfit = $this->manipulateBarChart($monthlyProfit, 'profit');

        return ['receiving' => $monthlyArrayReceive, 'sales' => $monthlyArraySale, 'profit' => $monthlyArrayProfit];
    }

    public function manipulateBarChart($chartData, $key)
    {

        $dataArray = array(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

        foreach ($chartData as $data) {

            $dataArray[$data->month - 1] = $data[$key];
        }

        return $dataArray;
    }

    public function lineChartData($branchId)
    {
        $profit = array();
        $days = array();

        $sevenDaysProfit = Order::getSevenDaysProfit($branchId);

        foreach ($sevenDaysProfit as $dailyProfit) {
            $date = $dailyProfit->date;
            $day = date("D", strtotime($date));
            array_push($profit, $dailyProfit->profit);
            array_push($days, $day);
        }

        return ['days' => $days, 'profit' => $profit];
    }
    public function setBranchId($branchId){        
        \DB::table('users')->where('id', \Auth::user()->id)->update(['dashboard_branch_id' => $branchId]);
    }
}