<?php

namespace App\Exports;

use App\Http\Controllers\API\PermissionController;
use App\Models\Product;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class StockReport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public $filterData;
    public $searchValue;
    public $branchIds;

    function __construct($filterData, $searchValue, $branchIds){
        $this->filterData = $filterData; 
        $this->searchValue = $searchValue;
        $this->branchIds = $branchIds;

    }

    public function query()
    {
        $_searchValue = $this->searchValue;
        $_filterData  = $this->filterData;

        $query = Product::query()->leftJoin('product_variants', 'product_variants.product_id', '=', 'products.id')
                ->leftJoin('product_groups', 'product_groups.id', '=', 'products.group_id')
                ->leftJoin('product_brands', 'product_brands.id', '=', 'products.brand_id')
                ->leftJoin('product_categories', 'product_categories.id', '=', 'products.category_id')  
                ->select(
                    'products.title',
                    'product_variants.product_id',
                    'product_variants.id as variant_id',
                    'product_variants.sku as sku',
                    'product_variants.bar_code as bar_code',
                    'product_groups.name as group_name',
                    'product_brands.name as brand_name',
                    'product_categories.name as category_name',
                    \DB::raw("CASE WHEN product_variants.variant_title = 'default_variant' THEN '-' ELSE product_variants.variant_title END AS variant_title")                
                ); 
        if ($_searchValue) {
            $query->where(function ($query) use ($_searchValue) {
                $query->where('products.title', 'LIKE', '%' . $_searchValue . '%');
                $query->orWhere('product_variants.variant_title', 'LIKE', '%' . $_searchValue . '%'); 
            });
        }
        if (!empty($_filterData)) {
            foreach ($_filterData as $singleFilter) {
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "product_name") {
                    $query->where('products.id', $singleFilter['value']);
                } 
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "brand_name") {
                    $query->where('product_brands.id', $singleFilter['value']);
                }
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "category_name") {
                    $query->where('product_categories.id', $singleFilter['value']);
                }
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "group_name") {
                    $query->where('product_groups.id', $singleFilter['value']);
                } 
            }
        }
        
        return $query;
 
    }
    public function map($reportRow): array
    {
        $_filterData = $this->filterData;

        // Opening stock calculation - sum of all purchases minus all sales before date range
        $openingPurchaseQry = DB::table('products')
            ->select(DB::raw('SUM(order_items.quantity) as opening_purchase_qty'),
                     DB::raw('SUM(order_items.quantity * order_items.price) as opening_purchase_value'))
            ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
            ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
            ->where('products.id', $reportRow->product_id)
            ->where('order_items.variant_id', $reportRow->variant_id)
            ->where('orders.status', 'done')
            ->where('orders.order_type', '!=', 'sales');

        $openingSalesQry = DB::table('products')
            ->select(DB::raw('SUM(order_items.quantity) as opening_sales_qty'),
                     DB::raw('SUM(order_items.quantity * order_items.price) as opening_sales_value'))
            ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
            ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
            ->where('products.id', $reportRow->product_id)
            ->where('order_items.variant_id', $reportRow->variant_id)
            ->where('orders.status', 'done')
            ->where('orders.order_type', 'sales');

        // Purchase data (within date range, non-sales orders)
        $productQry2purchase = DB::table('products')
            ->select(DB::raw('SUM(order_items.quantity) as purchase_qty'),
                     DB::raw('SUM(order_items.quantity * order_items.price) as purchase_value'))
            ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
            ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
            ->where('products.id', $reportRow->product_id)
            ->where('order_items.variant_id', $reportRow->variant_id)
            ->where('orders.status', 'done')
            ->where('orders.order_type', '!=', 'sales');

        // Sales data (within date range, sales orders)
        $productQry2Sales = DB::table('products')
            ->select(DB::raw('SUM(order_items.quantity) as sale_qty'),
                     DB::raw('SUM(order_items.quantity * order_items.price) as sale_value'))
            ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
            ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
            ->where('products.id', $reportRow->product_id)
            ->where('order_items.variant_id', $reportRow->variant_id)
            ->where('orders.status', 'done')
            ->where('orders.order_type', 'sales');

        // Apply date filters if provided
        if (!empty($_filterData)) {
            foreach ($_filterData as $singleFilter) {
                if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                    $starts = $singleFilter['value'][0]['start'];
                    $ends = $singleFilter['value'][0]['end'];

                    // For opening stock, get data before the start date
                    $openingPurchaseQry->where('orders.date', '<', $starts);
                    $openingSalesQry->where('orders.date', '<', $starts);

                    // For purchase and sales, get data within the date range
                    $productQry2purchase->whereBetween('orders.date', [$starts, $ends]);
                    $productQry2Sales->whereBetween('orders.date', [$starts, $ends]);
                }
            }
        }

        $openingPurchaseData = $openingPurchaseQry->groupBy('order_items.variant_id')->first();
        $openingSalesData = $openingSalesQry->groupBy('order_items.variant_id')->first();
        $purchaseData = $productQry2purchase->groupBy('order_items.variant_id')->first();
        $salesData = $productQry2Sales->groupBy('order_items.variant_id')->first();

        // Calculate opening stock as purchases minus sales before date range
        $opening_purchase_qty = isset($openingPurchaseData->opening_purchase_qty) ? $openingPurchaseData->opening_purchase_qty : 0;
        $opening_purchase_value = isset($openingPurchaseData->opening_purchase_value) ? $openingPurchaseData->opening_purchase_value : 0;
        $opening_sales_qty = isset($openingSalesData->opening_sales_qty) ? $openingSalesData->opening_sales_qty : 0;
        $opening_sales_value = isset($openingSalesData->opening_sales_value) ? $openingSalesData->opening_sales_value : 0;

        $opening_qty = $opening_purchase_qty - $opening_sales_qty;
        $opening_value = $opening_purchase_value - $opening_sales_value;

        $purchase_qty = isset($purchaseData->purchase_qty) ? $purchaseData->purchase_qty : 0;
        $purchase_value = isset($purchaseData->purchase_value) ? $purchaseData->purchase_value : 0;
        $sale_qty = isset($salesData->sale_qty) ? $salesData->sale_qty : 0;
        $sale_value = isset($salesData->sale_value) ? $salesData->sale_value : 0;
        $inventory_qty = ($opening_qty + $purchase_qty) - $sale_qty;
        $inventory_value = ($opening_value + $purchase_value) - $sale_value;

        return [
            $reportRow->category_name,
            $reportRow->group_name,
            $reportRow->brand_name,
            $reportRow->sku,
            $reportRow->bar_code,
            $reportRow->title,
            $reportRow->variant_title,
            $opening_qty ? $opening_qty : 0,
            ($opening_qty != 0) ? round($opening_value, 2) : 0,
            $purchase_qty ? $purchase_qty : 0,
            ($purchase_qty != 0) ? round($purchase_value, 2) : 0,
            $sale_qty ? $sale_qty : 0,
            ($sale_qty != 0) ? round($sale_value, 2) : 0,
            $inventory_qty ? $inventory_qty : 0,
            ($inventory_qty != 0) ? round($inventory_value, 2) : 0,
        ];

    }
    public function map1($reportRow): array
    {
        




        $_filterData  = $this->filterData;
        $_searchValue = $this->searchValue;
 
        $productQry = DB::table('products')
        ->select(DB::raw('SUM(order_items.quantity) as opening_qty'), 'orders.date')
        ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
        ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
        ->where('products.id', $reportRow->product_id)
        ->where('order_items.variant_id', $reportRow->variant_id)
        ->where('orders.status', 'done');  
            
        if (!empty($_filterData)) {
            foreach ($_filterData as $singleFilter) {
                if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                    $starts = $singleFilter['value'][0]['start'];
                    $ends = $singleFilter['value'][0]['end']; 
                    $productQry->where('orders.date', '<', $starts);
                } 
            }   
        } 
        $openingData = $productQry->groupBy('order_items.variant_id')->first(); 

        #purchaseData
        $productQry2purchase = DB::table('products')
        ->select(DB::raw('SUM(order_items.quantity) as purchase_qty'), 'orders.date')
        ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
        ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
        ->where('products.id', $reportRow->product_id)
        ->where('order_items.variant_id', $reportRow->variant_id)
        ->where('orders.status', 'done')  
        ->where('orders.order_type', '!=','sales');     
        if (!empty($_filterData)) {
            foreach ($_filterData as $singleFilter) {
                if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                    $starts = $singleFilter['value'][0]['start'];
                    $ends = $singleFilter['value'][0]['end']; 
                    $productQry2purchase->whereBetween('orders.date', [$starts, $ends]);
                } 
            }   
        } 
        $purchaseData = $productQry2purchase->groupBy('order_items.variant_id')->first(); 

        #saleData
        $productQry2Sales = DB::table('products')
        ->select(DB::raw('-SUM(order_items.quantity) as sale_qty'), 'orders.date')
        ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
        ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
        ->where('products.id', $reportRow->product_id)
        ->where('order_items.variant_id', $reportRow->variant_id)
        ->where('orders.status', 'done')  
        ->where('orders.order_type', 'sales');     
        if (!empty($_filterData)) {
            foreach ($_filterData as $singleFilter) {
                if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                    $starts = $singleFilter['value'][0]['start'];
                    $ends = $singleFilter['value'][0]['end']; 
                    $productQry2Sales->whereBetween('orders.date', [$starts, $ends]);
                } 
            }   
        } 
        $salesData = $productQry2Sales->groupBy('order_items.variant_id')->first(); 
        

        $opening_qty        = isset($openingData->opening_qty) ? $openingData->opening_qty : 0;
        $purchase_qty       = isset($purchaseData->purchase_qty) ? $purchaseData->purchase_qty : 0;
        $sale_qty           = isset($salesData->sale_qty) ? $salesData->sale_qty : 0;
        $inventory_qty      = ($opening_qty + $purchase_qty ) - $sale_qty;
            
        return [
            $reportRow->category_name,
            $reportRow->group_name,
            $reportRow->brand_name,
            $reportRow->sku,
            $reportRow->bar_code,
            $reportRow->title,
            $reportRow->variant_title,
            $opening_qty ? $opening_qty : '0',
            $purchase_qty ?$purchase_qty : '0',
            $sale_qty ? $sale_qty : '0',
            (int)$inventory_qty ? (int)$inventory_qty : '0'
        ];
    }

    public function headings(): array
    {
        return [
            'Category Name',
            'Group Name',
            'Brand Name',
            'SKU',
            'Barcode',
            Lang::get('lang.product_name'),
            Lang::get('lang.product_size'),
            Lang::get('lang.opening_stock'),
            Lang::get('lang.opening_stock_value'),
            'Purchases',
            Lang::get('lang.purchase_value'),
            'Sales',
            Lang::get('lang.sales_value'),
            'Inventory',
            Lang::get('lang.inventory_value'),
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:F1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );
  
            },
        ];
    }

}
