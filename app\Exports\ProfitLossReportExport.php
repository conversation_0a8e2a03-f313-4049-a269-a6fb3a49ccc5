<?php

namespace App\Exports;

use App\Http\Controllers\API\PermissionController;
use App\Libraries\AllSettingFormat;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class ProfitLossReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {
        $perm = new PermissionController();
        $permission = $perm->checkProfitPermission();
        $columns = [
            'item_tax' => DB::raw('sum(orders.total_tax) as item_tax'),
            'grand_total' => DB::raw('sum(orders.total) as grand_total'),
            'profit_amount' => DB::raw('sum(orders.profit) as profit_amount'),
            'date' => 'orders.date as date',
            'customer_id' => 'orders.customer_id as customer_id',
            'invoice_id' => 'orders.invoice_id as invoice_id',
            'year' => DB::raw('YEAR(orders.date) year'),
            'month' => DB::raw('MONTH(orders.date) month'),
            'customer' => DB::raw("CONCAT(customers.first_name,' ',customers.last_name)  AS customer"),
        ];
        $query = Order::select(
            $columns['year'],
            $columns['item_tax'],
            $columns['grand_total'],
            $columns['profit_amount']
        )->leftJoin('customers', 'customers.id', '=', 'orders.customer_id')
            ->where('orders.status', 'done')
            ->where('orders.order_type', 'sales')
            ->groupBy('year');


        if ($permission == 'personal') {
            $query->where('orders.created_by', Auth::user()->id);
        }
        return $query;
    }

    public function map($reportRow): array
    {
        $allSettingFormat = new AllSettingFormat;
        return
            [
                $reportRow->year,
                $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->grand_total, 2, '.', '')),
                $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->item_tax, 2, '.', '')),
                $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->profit_amount, 2, '.', '')),
            ];
    }

    public function headings(): array
    {
        return [
            "Year",
            "Grand Total",
            "Tax",
            "Profit Amount"
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:D1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );


                $query_result = $this->query()->get();
                $rows_count = count($query_result);

                $tax = $query_result->sum('item_tax');
                $grand_total = $query_result->sum('grand_total');
                $profit_amount = $query_result->sum('profit_amount');
                $rows_count = $rows_count + 3;

                $allSettingFormat = new AllSettingFormat;
                $event->sheet->setCellValue('A' . $rows_count, 'Grand Total');
                $event->sheet->setCellValue('B' . $rows_count, $allSettingFormat->getCurrencySeparator($grand_total));
                $event->sheet->setCellValue('C' . $rows_count, $allSettingFormat->getCurrencySeparator($tax));
                $event->sheet->setCellValue('D' . $rows_count, $allSettingFormat->getCurrencySeparator($profit_amount));

            },
        ];
    }

}
