<?php

return array(

    /*
    |--------------------------------------------------------------------------
    | Don't change anything in this file. This will be overwritten by updates.
    |--------------------------------------------------------------------------
    |
    | If you would like to change any language text, please copy the key from here to the custom.php
    |
    */

    //home page
    'welcome_to_gp' => 'Welcome to Gain POS',

    // Buttons
    'clear' => 'Clear',
    'apply' => 'Apply',
    'submit' => 'Submit',
    'save' => 'Save',
    'add' => 'Add',
    'edit' => 'Edit',
    'confirm' => 'Confirm',
    'cancel' => 'Cancel',
    'yes' => 'Yes',
    'no' => 'No',
    'enable_user' => 'Enable user',
    'disable_user' => 'Disable user',

    // Warning
    'are_you_sure' => 'Are you sure?',
    'service_deleted_permanently' => 'This service will be deleted permanently.',
    'branch_deleted_permanently' => 'This branch will be deleted permanently.',
    'sales_channel_deleted_permanently' => 'This sales channel will be deleted permanently.',
    'payment_deleted_permanently' => 'This payment will be deleted permanently.',
    'category_deleted_permanently' => 'This category will be deleted permanently.',
    'group_deleted_permanently' => 'This group will be deleted permanently.',
    'brand_deleted_permanently' => 'This brand will be deleted permanently.',
    'variant_attribute_deleted_permanently' => 'This variant attribute will be deleted permanently.',
    'unit_deleted_permanently' => 'This unit will be deleted permanently.',
    'tax_deleted_permanently' => 'This tax will be deleted permanently.',
    'order_will_be_deleted' => 'This order will be deleted.',
    'order_will_be_cancelled' => 'This order will be cancelled.',
    'this_payment_method_deleted_permanently' => 'This payment method will be deleted permanently.',
    'app_title' => 'Gain POS',
    'login' => 'Login',
    'login_email' => 'Email',
    'login_password' => 'Password',
    'forgot_password' => 'Forgot Password?',
    'dont_have_account' =>'Don\'t have an account?',
    'sign_up' => 'Sign Up',
    'login_here' => 'Login Here',
    'hi_there' => 'Hi There!',
    'sign_in_to_your_dashboard' => 'Sign in to your dashboard.',
    'sign_up_for_your_new_account' => 'Sign up for your new account.',
    'failed' => 'These credentials do not match our records.',
    'throttle' => 'Too many login attempts. Please try again in :seconds seconds.',
    'password' => 'Passwords must be at least six characters and match the confirmation.',
    'reset' => 'Your password has been reset!',
    'sent' => 'We have e-mailed your password reset link!',
    'token' => 'This password reset token is invalid.',
    'user' => 'We can\'t find a user with that e-mail address.',
    'previous' => '&laquo; Previous',
    'next' => 'Next &raquo;',
    'first_name' => 'First Name',
    'enter_first_name' => 'Enter first name',
    'last_name' => 'Last Name',
    'enter_last_name' => 'Enter last name',
    'full_name' => 'Full name',
    'confirm_password' => 'Confirm password',
    'already_have_an_account?' => 'Already have an account?',

    // Login Controller
    'inactive_invalid_email' => 'The selected email is invalid or you need to verify your mail address.',
    'email_or_password_is_wrong' => 'Email and password does not match.',

    // Auth Controller
    'account_already_verified' => 'Account already verified...',
    'successfully_verified' => 'You have successfully verified your email address.',
    'verification_code_invalid' => 'Verification code is invalid.',
    'email_not_found' => 'The selected email is invalid or your email address was not found.',
    'password_reset' => 'Your Password Reset Link',
    'reset_email_send' => 'A reset email has been send! Please check your email.',
    'password_reset_successful' => 'Password reset successful.',
    'password_reset_is_not_successful' => 'Password reset is not successful.',

    // Register Controller
    'registration_confirmation' => 'Registration Confirmation',
    'account_verification' => 'Account Verification',
    'pos_invoice' => 'Invoice',
    'confirmation_email_send' => 'Confirmation email has been send. please check your email.',
    'activation_completed' => 'Your activation is completed.',
    'something_wrong' => 'Whoops, Something went wrong!!!',

    // API User Controller
    'verify_email' => 'We can\'t find an account with this credentials. Please make sure you entered the right information and you have verified your email address.',
    'logout' => 'You have successfully logged out.',
    'logout_failed' => 'Failed to logout, please try again.',
    'signup_welcome' => 'Thanks for signing up! Please check your email to complete your registration.',
    'error_update' => 'Error during update',
    'update_successful' => 'Updated successfully.',
    'delete_fail' => 'Deleted failed.',
    'delete' => 'Successfully Deleted.',
    'getting_problems' => 'Opps, something went wrong!',

    // Profile Information
    'profile_title' => 'My Profile',
    'date_of_birth' => 'Date Of Birth',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'others' => 'Others',
    'change_profile_image' => 'Change Profile Image',
    'image_only' => 'Choose Images Only',
    'profile_update_success' => 'Profile update successfully.',
    'profile' => 'Profile',

    // Account update
    'account_update' => 'Account Update',

    //Reset Password
    'enter_email_address' => 'Enter your email address to reset your password.',
    'enter_email' => 'Enter Email',
    'enter_password' => 'Enter Password',
    'send' => 'Send',
    'reset_password' => 'Reset Password',
    'new_password' => 'New password',
    'change_password' => 'Change Password',
    'password_not_match' => 'Password confirmation doesn\'t match.',
    'password_change_success' => 'Successfully password change.',
    'register' => 'Register',

    // Basic Setting
    'general_settings' => 'General Settings',
    'application_settings' => 'Application Settings',
    'application_settings_message' => 'Application settings',
    'app_name' => 'Application name',
    'change_app_logo' => 'Change application logo',
    'change_background_image' => 'Change background image',
    'rows_per_table' => 'Rows per table',
    'choose_currency' => 'Choose Currency',
    'date_time_settings' => 'Date/Time Settings',
    'time_format' => 'Time format',
    'timezone_title' => 'Timezone',
    '24h' => '24h',
    '12h' => '12h',
    'date-format' => 'Date format',
    'dd/mm/yyyy' => 'DD/MM/YYYY',
    'mm/dd/yyyy' => 'MM/DD/YYYY',
    'yyyy/mm/dd' => 'YYYY/MM/DD',
    'dd-mm-yyyy' => 'DD-MM-YYYY',
    'mm-dd-yyyy' => 'MM-DD-YYYY',
    'yyyy-mm-dd' => 'YYYY-MM-DD',
    'dd_mm_yyyy' => 'DD.MM.YYYY',
    'mm_dd_yyyy' => 'MM.DD.YYYY',
    'yyyy_mm_dd' => 'YYYY.MM.DD',
    'd_mmmm_yyyy' => 'd mmmm, yyyy',
    'time_zone' => 'Time Zone',
    'currency_settings' => 'Currency Settings',
    'currency_symbol' => 'Currency symbol',
    'currency_position' => 'Currency position',
    'thousand_separator' => 'Thousand separator',
    'decimal_separator' => 'Decimal separator',
    'number_of_decimal' => 'Number of decimal',
    'language_settings' => 'Language Settings',
    'english' => 'English',
    'preferred_language' => 'Preferred language',
    'select_branch' => 'Select Branch',
    'application' => 'Application',
    'emails' => 'Email',
    'email_template' => 'Email Template',
    'off_day' => 'Off Days',
    'holiday' => 'Holiday',
    'users' => 'Users',
    'payment_types' => 'Payment Types',
    'all_product' => 'All Product',
    'edit_type' => 'Edit Payment Type',
    'add_type' => 'Add Payment Type',
    'payment' => 'Payment',
    'round' => 'Round',
    'no_round' => 'No Rounding',
    'near_integer' => 'Rounding Nearest Integer',
    'near_half' => 'Rounding Nearest Half',
    'sales_channels' => 'Sales Channels',
    'sales_channel' => 'Sales Channels',
    'channel_name' => 'Channel Name',
    'channel_type' => 'Channel Type',
    'edit_channel' => 'Edit Channel',
    'add_channel' => 'Add Channel',
    'tax_settings' => 'Tax Settings',
    'taxes' => 'Taxes',
    'tax' => 'Tax',
    'included' => 'Inclusive',
    'excluded' => 'Exclusive',
    'product_price_tax' => 'Product Price Tax',
    'applicable_tax_is_default' => 'Applicable Tax Is Default',
    'add_tax' => 'Add Tax Item',
    'edit_tax' => 'Edit Tax Item',
    'branches' => 'Branches',
    'add_branch' => 'Add Branch',
    'edit_branch' => 'Edit Branch',
    'percentage' => 'Percentage',
    'tax_item' => 'Tax item',
    'shortcuts' => 'Shortcuts Settings',
    'keyboard_shortcut' => 'Keyboard Shortcut',
    'use_ctrl_or_shift_for_combination_key' => 'Use only Ctrl or Shift for combination key',

    // notification
    'notification_time' => 'Notification time',
    'add_notification_time' => 'Add Notification time',
    'notification_settings_saved_successfully' => 'Notification settings saved successfully.',
    'notify_manager_for_low_stock' => 'Notify manager for low stock',

    // corn job
    'corn_job' => 'Cron Job',
    'corn_job_link' => 'Cron job link',
    'last_corn_job_run' => 'Last cron job run',
    'recommended_execution_interval' => 'Recommended execution interval',
    'cpanel_corn_job_command' => 'cPanel cron job command',
    'daily_one' => 'Once in a Day',
    'never' => 'Never',

    // branch
    'branch_manager_up' => 'Branch Manager',
    'branch_manager_sm' => 'Branch manager',
    'no_branch_manager' => 'No manager',

    //booking filter
    'cancelled' => 'Cancelled',
    'confirmed_' => 'Confirmed',
    'pending' => 'Pending',
    'on_the_way' => 'On the way',
    'delivered' => 'Delivered',
    'packet' => 'Packet',
    'done' => 'Done',
    'all' => 'All',
    'today' => 'Today',
    'next_7_days' => 'Next 7 Days',
    'this_month' => 'This month',
    'this_year' => 'This year',

    // Roles
    'roles' => 'Roles',
    'role_title' => 'Role title',
    'role_deleted_permanently' => 'This role will be deleted permanently.',
    'edit_role' => 'Edit Role',
    'add_role' => 'Add Role',
    'role' => 'Role',
    'can_manage_application_settings' => 'Can manage application settings',
    'can_see_application_settings' => 'Can see application settings',
    'can_manage_email_settings' => 'Can manage email settings',
    'can_see_email_settings' => 'Can see email settings',
    'can_manage_email_templates' => 'Can manage email templates',
    'can_see_email_templates' => 'Can see email templates',
    'can_manage_branches' => 'Can manage branches',
    'can_see_branches' => 'Can see branches',
    'can_manage_sales_channels' => 'Can manage sales channels',
    'can_manage_payment_settings' => 'Can manage payment settings',
    'can_see_payment_settings' => 'Can see payment settings',
    'can_manage_tax_settings' => 'Can manage tax settings',
    'can_see_tax_settings' => 'Can see tax settings',
    'can_manage_product_setting' => 'Can manage product settings',
    'can_see_product_setting' => 'Can see product settings',
    'can_manage_adjust_stock' => 'Can manage adjust stock settings',
    'can_see_adjust_stock_settings' => 'Can see adjust stock settings',
    'can_manage_keyboard_shortcuts' => 'Can manage keyboard shortcuts',
    'can_manage_discount_coupons' => 'Can manage discount coupons',
    'can_close_others_cash_register' => 'Can close others cash register',
    'can_manage_products' => 'Can manage products',
    'can_see_products' => 'Can see products',
    'can_manage_categories' => 'Can manage categories',
    'can_see_categories' => 'Can see categories',
    'can_manage_brands' => 'Can manage brands',
    'can_see_brands' => 'Can see brands',
    'can_manage_groups' => 'Can manage groups',
    'can_see_groups' => 'Can see groups',
    'can_manage_variant_attribute' => 'Can manage variant attributes',
    'can_see_variant_attribute' => 'Can see variant attributes',
    'can_manage_units' => 'Can manage units',
    'can_see_units' => 'Can see units',
    'can_manage_customers' => 'Can manage customers',
    'can_see_customers' => 'Can see customers',
    'can_manage_customer_groups' => 'Can manage customer groups',
    'can_see_customer_groups' => 'Can see customer groups',
    'can_manage_sales' => 'Can manage sales',
    'cash_register' => 'Cash Register',
    'cash_register_label' => 'Cash register',
    'can_manage_receives' => 'Can manage purchases',
    'can_manage_updates_setting' => 'Can manage updates setting',
    'can_see_updates_setting' => 'Can see updates setting',
    'can_manage_table_setting' => 'Can manage table setting',
    'can_see_table_setting' => 'Can see table setting',
    'can_see_invoice_template_setting' => 'Can see invoice template setting',
    'can_manage_invoice_template_setting' => 'Can manage invoice template setting',
    'can_manage_purchase_invoice_setting' => 'Can manage purchase invoice setting',
    'can_see_purchase_invoice_setting' => 'Can see purchase invoice setting',
    'can_see_notification_setting' => 'Can see notification setting',
    'can_manage_notification_setting' => 'Can manage notification setting',
    'can_see_corn_job_setting' => 'Can see cron job setting',
    'can_manage_corn_job_setting' => 'Can manage cron job setting',
    'can_see_sales_setting' => 'Can see sales setting',
    'can_manage_sales_setting' => 'Can manage sales setting',
    'can_manage_sms_setting' => 'Can manage sms setting',
    'can_see_sms_setting' => 'Can see sms setting',

    //Email Settings
    'email_settings' => 'Email Settings',
    'application_name' => 'Email sent from name',
    'email_address' => 'Email Address',
    'email_driver' => 'Email driver',
    'host' => 'Host',
    'port' => 'Port',
    'encryption_type' => 'Encryption type',
    'password_email_settings' => 'Password to access ',
    'test_mail' => 'Test mail',
    'test_send' => 'Test Sned',
    'test_email_sent' => 'Test mail is sent',
    'email_settings_successfully_saved_&_ test_mail_is_sent' => 'Email settings saved successfully & test mail has been sent.',
    'type_mail_address_to_check_email_config' => 'Type an email address if you want to check email configuration',
    'smtp' => 'SMTP',
    'sendmail' => 'Send Mail',
    'tls' => 'TLS',
    'ssh' => 'SSH',
    'email_not_sent' => 'Unable to send email! Please check email settings.',
    'decimal_separator_and_thousand_separator_are_same' => 'Decimal separator and thousand separator can’t be same.',
    'sms_settings' => 'Sms Settings',
    'from_name_phone' => 'Sms send from name/phone number',
    'sms_driver' => 'Sms driver',
    'nexmo' => 'Nexmo',
    'key' => 'Key',
    'secret_key' => 'Secret key',
    'auto_sms_received_to_customer' => 'Auto sales sms recived to customer',

    //Email template
    'user_invitation' => 'User Invitation',
    'user_registration' => 'User Registration',
    'booking_confirmation' => 'Booking Confirmation',
    'low_stock' => 'Low Stock Template',
    'booking_received' => 'Booking Received',
    'email_templates' => 'Email Templates',
    'sms_templates' => 'Sms Templates',
    'subject' => 'Subject',
    'content' => 'Content',
    'booking_rejected' => 'Booking Rejected',
    'type' => 'Type',
    'email_template_restored_to_default' => 'Email template has been restored to default.',
    'restore_default' => 'Restore to Default',
    'sms_content' => 'Sms content',

    // Off Day Setting
    'off_day_settings' => 'Off Day Settings',
    'add_off_days' => 'Add off days',
    'sunday' => 'Sunday',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',
    'saturday' => 'Saturday',

    // Product Setting
    'sku_prefix' => 'SKU prefix',
    'enable_unit' => 'Enable Unit',
    'enable_brands' => 'Enable Brands',
    'enable_categories' => 'Enable Categories',
    'enable_groups' => 'Enable Groups',

    // Updates Setting
    'updates' => 'Updates',
    'updates_setting' => 'Updates Setting',
    'no_updates_available' => 'No Updates Available',
    'purchase_key' => 'Purchase key',
    'purchase_key_saved_successfully' => 'Purchase key saved successfully.',

    // Table setting
    'restaurant_tables' => 'Restaurant Tables',
    'table' => 'Table',
    'tables' => 'Tables',
    'table_name' => 'Table Name',
    'table_name_label' => 'Table name',
    'table_capacity' => 'Table Capacity',
    'table_details' => 'Table Details',
    'edit_table' => 'Edit Table',
    'add_table' => 'Add Table',

    // Navbar
    'dashboard' => 'Dashboard',
    'sales' => 'Sales',
    'returns' => 'Returns',
    'due_payments' => 'Due Payments',
    'receiving' => 'Purchasing',
    'receives' => 'Purchases',
    'receives_summary' => 'Purchases Summary',
    'services' => 'Services',
    'booking' => 'Booking',
    'reports' => 'Reports',
    'settings' => 'Settings',
    'logout_nv' => 'Logout',
    'shortcut_list' => 'Shortcut List',
    'your_notifications' => 'Your notifications',
    'view_all_notifications' => 'View all notifications',
    'all_notifications' => 'All notifications',
    'view_all' => 'View all',
    'no_result_found' => 'No result found',
    'required_input_field' => 'This field is required',
    'hold_order_list' => 'Hold Order List',

    // User list
    'user_list' => 'User list',
    'invite_user' => 'Invite User',
    'invite_button' => 'Invite',
    'invite_as' => 'Invite as',
    'invitation_sent_to' => 'Invitation sent to ',
    'invitation_error' => 'Invitation email is invalid or does not exist',
    'name' => 'Name',
    'change_user_role' => 'Change User Role',

    // Dashboard
    'total_booking_for_next_30_days' => 'Total booking for next 30 days',
    'confirmed_booking_for_next_30_days' => 'Confirmed booking for next 30 days',
    'today_total_booking' => 'Today\'s total booking',
    'today_pending_booking' => 'Today\'s pending booking',
    'pos_overview' => 'POS overview',
    'last_12_months' => 'Last 12 months',
    'last_7_days' => 'Last 7 days',
    'last_30_days' => 'Last 30 days',
    'booking_type' => 'Booking types',
    'total_bookings_of_different_services' => 'Total bookings of different services',
    'total_booking' => 'Total Booking',
    'last_month' => 'Last month',
    'last_year' => 'Last year',
    'till_now' => 'Till now',
    'today_sales_invoice' => 'Today\'s Sales Invoice',
    'today_total_tax' => 'Today\'s Tax',
    'total_product' => 'Total Products',
    'today_profit' => 'Today\'s Profit',
    'last_thirty_days_profit' => 'Last 30 Day\'s Profit',
    'total_profit' => 'Total Profit',
    'sales_return' => 'Sales Return',
    'sales_list' => 'Sales List',
    'sales_list_small' => 'Sales',
    'sales_list_deleted_permanently' => 'Sales will be deleted permanently.',
    'shipment_list' => 'Shipment List',

    // Controller Message
    'error_during_update' => 'Error during update',
    'permission_updated' => 'Permission Updated',
    'permission_error' => 'Permission Error',
    'permission_is_not_available' => 'Permission is not available',
    'created_successfully' => 'Created Successfully.',
    'error_during_creating' => 'Error during creating',
    'registration_done' => 'Your registration is done',
    'notification_open' => 'Notification open',
    'password_updated' => 'Password updated',
    'error_during_deleted' => 'Error during Deleted',
    'date_range' => 'Date range',
    'customs' => 'Custom',
    'notification' => 'Notification',
    'booking_notification' => 'Booking Notification',
    'pos_notification' => 'POS Notification',
    'in_use' => 'in use',
    'you_can_not_delete_the' => 'you can not delete the',
    'successfully_added' => 'added successfully.',
    'successfully_saved' => 'saved successfully.',
    'successfully_deleted' => 'deleted successfully.',
    'successfully_updated' => 'updated successfully.',
    'offline_alert' => 'You are offline but don’t worry, you can continue your sales and we’ll sync the sales when will be online again. Please don’t close the browser.',
    'online_alert' => 'You are online. We are syncing your sales.',

    // Product
    'product' => 'Product',
    'product_title' => 'Product Title',
    'product_details' => 'Product Details',
    'products' => 'Products',
    'description' => 'Description',
    'product_category' => 'Product Category',
    'product_group' => 'Product Group',
    'categories' => 'Categories',
    'all_products' => 'All Products',
    'product_categories' => 'Product Categories',
    'category' => 'Category',
    'edit_category' => 'Edit Category',
    'add_category' => 'Add Category',
    'category_name' => 'Category Name',
    'brands' => 'Brands',
    'product_brands' => 'Product Brands',
    'product_brand' => 'Product Brand',
    'add_brand' => 'Add Brand',
    'edit_brand' => 'Edit Brand',
    'product_groups' => 'Product Groups',
    'add_new_product' => 'Add New Product',
    'edit_product' => 'Edit Product',
    'group' => 'Group',
    'brand' => 'Brand',
    'standard_product' => 'Standard product',
    'variant_product' => 'Variant product',
    'variant' => 'Variant',
    'value' => 'Value',
    'product_attributes' => 'Product Variant Attributes',
    'edit_attribute' => 'Edit Variant Attribute',
    'edit_unit' => 'Edit Unit',
    'add_new_attribute' => 'Add New Variant Attribute',
    'add_new_unit' => 'Add New Unit',
    'variant_attributes' => 'Variant Attributes',
    'price' => 'Price',
    'purchase_price' => 'Purchase Price',
    'selling_price' => 'Selling Price',
    'selling_price_label' => 'Selling price',
    'receiving_price' => 'Purchase Price',
    'purchase_price_label' => 'Purchase price',
    'product_deleted_permanently' => 'Product will be deleted permanently.',
    'quantity' => 'Quantity',
    'variants_title' => 'Variants Title',
    'tax_free_product' => 'Tax-free product',
    'tax_enabled_product' => 'Tax-enabled product',
    'tax_included' => 'Tax included',
    'tax_excluded' => 'Tax excluded',
    'product_variants' => 'Product Variants',
    'variants' => 'Variants',
    'tax_free' => 'Tax-free',
    'tax_enabled' => 'Tax-enabled',
    'upload_product_image' => 'Upload product image',
    'upload_variant_image' => 'Upload variant image',
    'variant_image' => 'Image',
    'browse' => 'Browse',
    'availability' => 'Availability',
    'sl' => 'Sl',
    'add_variant_details' => 'Add Variant Details',
    'add_product_variants' => 'Add Product Variants',
    'update_product_image' => 'Update Product Image',
    'add_another_variant' => 'Add another variant',
    'this_product_in_use' => 'This product in use, You can not delete this product',
    'upload' => 'Upload',
    'select_existing_or_add_new' => 'Select existing or add new',
    'import_opening_stock' => 'Import opening stock',
    'opening_stock_preview' => 'Opening Stock Preview',
    'import_product' => 'Import product',
    'upload_file_to_import_product' => 'Upload file to import product',
    'price_must_bellow_1000000' => 'Price must be bellow 1000000',
    'adjective_stock' => 'Add Initial Quantity',

    // customers
    'customer' => 'Customer',
    'customer_avatar' => 'Customer avatar',
    'customers' => 'Customers',
    'add_customer' => 'Add New Customer',
    'add_customer_label' => 'Add new customer',
    'customer_name' => 'Customer Name',
    'customer_email' => 'Email',
    'customer_mobile_number' => 'Mobile number',
    'edit_customer' => 'Edit Customer',
    'customer_company' => 'Company',
    'customer_code' => 'Customer Code',
    'customer_website' => 'Website',
    'customer_address' => 'Address',
    'customers_summary' => 'Customers Summary',

 
    

    
    
    'phone_number' => 'Phone number',
    'phone_number_datatable' => 'Phone Number',
    'mobile_number' => 'Mobile number',
    'users_type' => 'User Type',
    'client' => 'Client',
    'customer_type' => 'Customer Type',
    'customer_deleted_permanently' => 'Customer will be deleted permanently.',
    'customer_details' => 'Customer Details',
    'customer_info' => 'Customer Info',
    'customer_purchase_records' => 'Customer Purchase Record',
    'customer_type' => 'Student Type',
    'customer_deleted_permanently' => 'Student will be deleted permanently.',
    'customer_details' => 'Student Details',
    'customer_info' => 'Student Info',
    'customer_purchase_records' => 'Student Purchase Record',
    'your_format_is_not_correct' => 'Your Formate Is Not Correct',

    // groups
    'groups' => 'Groups',
    'default' => 'Default',
    'discount' => 'Discount',
    'default_discount' => 'Default discount',
    'add_new_group' => 'Add New Group',
    'edit_group' => 'Edit Group',
    'customer_group' => 'Customer group',
    'customer_group_dt' => 'Customer Group', 
    'number_of_customer' => 'Number of Customers',
    'search' => 'Search',
    'mark_as_default' => 'Mark as default',
    'this_group_will_be_selected_automatically_when_you_create_new_customer' => 'This group will be selected automatically when you create new customer',
    'this_discount_will_be_applicable_for_future_sales_for_the_customers_of_this_group' => 'This discount will be applicable for future sales for the customers of this group',
    'is_default' => 'Is default',
    'is_default_dt' => 'Is Default',

    //unit
    'unit' => 'Unit',

    //Sales Register
    'cash_registers' => 'Cash Registers',
    'cash_register_deleted_permanently' => 'This cash register will be deleted permanently.',
    'add_cash_register' => 'Add Cash Register',
    'edit_cash_register' => 'Edit Cash Register',
    'pay' => 'Pay',
    'empty_cart' => 'Empty Cart',

    //invoice settings
    'sales_invoice_settings' => 'Sales Invoice Settings',
    'purchase_invoice_settings' => 'Purchase Invoice Settings',
    'invoice_prefix' => 'Invoice prefix',
    'invoice_suffix' => 'Invoice suffix',
    'invoice_starts_from' => 'Invoice starts from',
    'change_invoice_logo' => 'Change invoice logo',
    'invoice_logo_saved_successfully' => 'Invoice logo saved successfully.',
    'auto_generate_receipt' => 'Auto generate receipt',
    'auto_email_receive' => 'Auto email receive to customer',
    'view_options_for_share_invoice_in_social_media' => 'View options for share invoice in social media',
    'the_number_is_already_used_for_invoice' => 'The number is already used for invoice',
    'invoice_number_already_in_use_but_others_settings_successfully_saved' => 'Invoice number already in use but others settings saved successfully.',
    'invoice_settings_small' => 'Invoice settings',
    'invoice_templates' => 'Invoice Templates',
    'share_this_invoice' => 'Share this invoice',

    //Users
    'user_details' => 'User Details',
    'user_records' => 'User Records',
    'enable_sales_delete' => 'Enable sales delete',
    'enable_edit_sale_date' => 'Enable edit sales date',
    'enable_sale_report_email' => 'Enable sales report email',

    //Reports
    'date' => 'Date',
    'time' => 'Time',
    'sales_reports' => 'Sales Reports',
    'sales_person' => 'Sales Person',
    'edit_sales' => 'Edit Sales',
    'sales_id' => 'Sales ID',
    'sales_date' => 'Date',
    'sales_type' => 'Sales type',
    'sales_type_dt' => 'Sales Type',
    'receive_type' => 'Purchase type',
    'item_purchased' => 'Item Purchased',
    'subtotal' => 'Subtotal',
    'total' => 'Total',
    'grand_total' => 'Grand Total',
    'sold_by' => 'Sold By',
    'sold_to' => 'Sold To',
    'sold_given_by' => 'Sales Person',
    'sales_report' => 'Sales Report',
    'customer_sales_report' => 'Customer Sales Report',
    'internal_sales' => 'Internal Sales',
    'sold_to_customer' => 'Sold to Customer',
    'sales_returns' => 'Sales Returns',
    'receivings' => 'Purchasing',
    'received_id' => 'Purchased ID',
    'received_date' => 'Date',
    'receiving_report' => 'Purchasing Reports',
    'received_type' => 'Purchased Type',
    'item_received' => 'Item Purchased',
    'received_by' => 'Purchased By',
    'received_from' => 'Purchased From',
    'supplier_avatar' => 'Supplier avatar',
    'internal_receivings' => 'Internal Purchasing',
    'register_logs' => 'Register Logs',
    'register_log_id' => 'ID',
    'employee' => 'Employee',
    'amount' => 'Amount',
    'opened_by' => 'Opened By',
    'closed_by' => 'Closed By',
    'cash_sales' => 'Cash Sales',
    'expense_amount' => 'Expenses',
    'cash_payment' => 'Cash Payment',
    'log_status' => 'Status',
    'difference' => 'Difference',
    'note' => 'Note',
    'register_title' => 'Title',
    'inventories' => 'Inventories',
    'inventory_id' => 'ID',
    'sku' => 'SKU',
    'barcode' => 'Barcode',
    'item_name' => 'Item Name',
    'group_name' => 'Group Name',
    'brand_name' => 'Brand Name',
    'cost_price' => 'Cost Price',
    'unit_price' => 'Unit Price',
    'inventory' => 'Inventory',
    'low_inventory' => 'Low inventory',
    'sales_summary' => 'Sales Summary',
    'group_by' => 'Group by',
    'payment_report' => 'Payments',
    'id' => 'ID',
    'payment_date' => 'Date',
    'paid' => 'Paid',
    'change' => 'Change',
    'paid_by' => 'Paid By',
    'in_stock' => 'In stock',
    'out_of_stock' => 'Out of stock',
    'default_re_order_quantity' => 'Default re-order quantity',
    're_order' => 'Re order',
    'payment_summary_report' => 'Payments Summary',
    'in_in' => 'In',
    'out' => 'Out',    
    'cash' => 'Cash',
    'credit' => "Credit",
    'upi' => "UPI",
    'card' => "Card",
    'receiving_id' => 'Purchasing Id',
    'tax_id' => 'Tax Id',
    'tax_date' => 'Date',
    'tax_order_type' => 'Order Type',
    'status' => 'Status',
    'tax_total' => 'Total tax',
    'due_total' => 'Total Due',
    'sales_date_edit' => 'Sales Date Edit',
    'sales_remarks_edit' => 'Sales Remarks Edit',
    'profit_loss' => 'Profit / Loss',
    'profit_and_loss' => 'Profit & Loss',
    'payment_method' => 'Payment Method',
    'payment_method_label' => 'Payment method',
    'profit_amount' => 'Profit Amount',
    'first_name & last_name is requir' => 'First name and last Name is requires',
    'this_email_are_already_exit' => 'This email already exist',
    'first_name_last_name_and_email_is_requir' => 'First name,last name and email is requires',
    'first_name_and_last_name_require_email_already_exit' => 'First name,last name requires,email already exits',
    'this_email_is_duplicate' => 'This email is duplicate',
    'first_name_and_last_name_require_email_is_duplicate' => 'First name,last name requires,email duplicate',
    'shipment_report' => 'Shipment Reports',

    //suppliers
    'suppliers' => 'Suppliers',
    'supplier' => 'Supplier',
    'suppliers_summary' => 'Suppliers Summary',
    'total_purchase' => 'Suppliers Purchase',
    'internal' => 'Internal',
    'supplier_info' => 'Supplier Info',
    'internal_transfer' => 'Internal Transfer',
    'supplier_invoice_id' => 'Supplier Invocie ID',
    'supplier_invoice_date' => 'Supplier Invocie Date',

    //sales and purchase report
    'sales_and_purchase' => 'Sales & Purchases',
    'sales_due' => 'Sales Due',
    'sales_tax' => 'Sales Tax',
    'purchase' => 'Purchase',
    'purchase_due' => 'Purchase Due',
    'purchase_tax' => 'Purchase Tax',
    'is_out_of_stock' => ' is out of stock!',

    //dashboards
    'dash_board_cash' => 'Today\'s Cash Register Amount',
    'open' => 'Open',
    'closed' => 'Closed',
    'close' => 'Close',
    'enroll' => 'Enroll',
    'choose_branch' => 'Choose Branch',
    'choose_one' => 'Choose one',
    'closing_amount' => 'Closing Amount',
    'closing_amount_label' => 'Closing amount',
    'opening_amount' => 'Opening Amount',
    'opening_amount_label' => 'Opening amount',
    'branch_name' => 'Branch name',
    'branch_type' => 'Branch type',
    'branch_type_dt' => 'Branch Type',
    'retail' => 'Retail',
    'restaurant' => 'Restaurant',
    'title' => 'Title',
    'sales_invoice_template' => 'Sales invoice template',
    'receiving_invoice_template' => 'Purchasing invoice template',
    'action' => 'Action',
    'not_yet' => 'Not Ready',
    'item_qtty' => 'Qty',
    'export' => 'Export',

    //reports roles
    'can_see_sales_reports' => 'Can see sales reports',
    'can_see_sales_details_reports' => 'Can see sales details reports',
    'can_see_report_action' => 'Can see report action',
    'can_see_personal_sales_reports' => 'Can see personal sales reports',
    'can_see_sales_person_sales_reports' => 'Can see sales person sales reports',
    'can_see_sales_summary_reports' => 'Can see sales summary reports',
    'can_see_receiving_reports' => 'Can see receiving reports',
    'can_see_personal_receiving_reports' => 'Can see personal receiving reports',
    'can_see_receiving_summary_reports' => 'Can see receiving summary reports',
    'can_see_customer_summary_reports' => 'Can see customer summary reports',
    'can_see_sales_and_purchase_reports' => 'Can see sales and purchase reports',
    'can_see_register_reports' => 'Can see register reports',
    'can_see_inventory_reports' => 'Can see inventory reports',
    'can_see_payment_reports' => 'Can see payment reports',
    'can_see_sales_payment_reports' => 'Can see sales payment reports',
    'can_see_payment_summary_reports' => 'Can see payment summary reports',
    'can_see_sales_statistics_reports' => 'Can see sales statistics reports',
    'can_see_tax_reports' => 'Can see sales tax reports',
    'can_see_profit_loss_reports' => 'Can see profit/loss reports',
    'today_sell' => 'Today\'s Sales',
    'last_seven_days_sales' => 'Last 7 Days Sales',
    'last_thirty_days_sales' => 'Last 30 Days Sales',
    'total_sales' => 'Total Sales',
    'total_return' => 'Total Return',
    'total_payment' => 'Total Payment',
    'today_receiving' => 'Today\'s Purchases',
    'last_thirty_days_receiving' => 'Last 30 Days Receives',
    'bar_chart_title' => 'Sales & Profit Overview',
    'sales_overview' => 'Sales Overview',
    'chart_sub_title' => 'This Year',
    'line_chart_sub_title' => 'This Week',
    'doughnut_chart_title' => 'Last 7 Days Profit',
    'data_table_search_type' => 'Type',
    'profit' => 'Profit',
    'payment_summary' => 'Payment Summary',
    'receiving_summary' => 'Purchasing Summary',
    'no_tax_added_default' => 'No tax is selected as default',

    //suppliers
    'can_manage_suppliers' => 'Can manage suppliers',
    'can_see_suppliers' => 'Can see suppliers',
    'can_see_supplier_summary_reports' => 'Can see supplier summary reports',

    //setting roles
    'it_can_be_dangerous' => 'This could be dangerous',
    'can_manage_invoice_setting' => 'Can manage invoice settings',
    'can_see_invoice_setting' => 'Can see invoice settings',
    'can_manage_roles' => 'Can manage roles',
    'can_see_roles' => 'Can see roles',
    'can_manage_cash_registers' => 'Can manage cash registers',
    'can_see_cash_registers' => 'Can see cash registers',
    'can_manage_users' => 'Can manage users',
    'can_see_users' => 'Can see users',

    //sales reports details
    'purchase_return' => 'Purchase Return',
    'available_quantity' => '. Available quantity ',
    'please_remove_from_cart' => ' Please remove from cart.',
    'order_id' => 'Order Id',
    'date_time' => 'Date & Time',
    'sales_report_sold_to' => 'Sold To',
    'paid_amount' => 'Paid Amount',
    'method' => 'Method',
    'sales_reports_sold_by' => 'Sold By',
    'cash_register_name' => 'Cash Register',
    'branch' => 'Branch',
    'sales_reports_item_name' => 'Item',
    'item_image' => 'Image',
    'item_qty' => 'Quantity',
    'stock_qty' => 'Stock Quantity',
    'item_price' => 'Price',
    'item_subtotal' => 'Subtotal',
    'item_tax' => 'Tax',
    'item_discount' => 'Discount',
    'item_total' => 'Total',
    'chose_product_type' => 'Choose Product Type',
    'sales_details' => 'Sales Details',
    'receives_details' => 'Purchases Details',
    'served_by' => 'Served by',
    'served_to' => 'Served to',
    'invoice_id' => 'Invoice ID',
    'items' => 'Items',
    'item' => 'Item',
    'qty' => 'Qty',
    'sub_total' => 'Sub Total',
    'exchange' => 'Exchange',
    'due' => 'Due',
    'payments' => 'Payments',
    'payment_status' => 'Payment Status',
    'done_payment' => 'Done Payment',
    'done_payment_label' => 'Done payment',
    'add_payment' => 'Add Payment',
    'payment_received' => 'Payment Received',
    'send_sms_customer' => 'Send sms customer',
    'send_sms' => 'Send sms',
    'print_receipt' => 'Print Receipt',
    'attribute_values' => 'Attribute Values',
    'included_tax' => 'Included Tax',
    'tax_name' => 'Tax Name',
    'product_type' => 'Product Type',
    'product_name_is_duplicate' => 'Product name is duplicate',
    'created_by' => 'Created By',
    'approved_by' => "Approved By",
    'have_you_forgot_your_password' => 'Have you forgot your password?',
    'click_here' => 'Click Here',
    'if_you_remember_your_password' => 'If you remember your password?',
    'forget_password' => 'Forgot Password',
    'clear_language_cache' => 'Clear language cache',
    'the_language_cache_has_been_removed' => 'The language cache has been removed.',
    'didnt_find' => 'We didn\'t find anything to show here.',
    'summery' => 'Summery',
    'item_sold' => 'Item Sold',
    'item_solds' => 'Items Sold',
    'supplier_code' => 'Supplier Code',
    'add_supplier' => 'Add New Supplier',
    'edit_supplier' => 'Edit Supplier',
    'add_sales_users' => 'Add User',
    'edit_sales_users' => 'Edit User',
    'supplier_details' => 'Supplier details',
    'sales_users_details' => 'User data',  
    'supplier_delivery_records' => 'Supplier delivery records ',
    'supplier_id' => 'ID',
    'supplier_date' => 'Date',
    'payment_type' => 'Payment type',
    'received_branch' => 'Purchased Branch',
    'receiving_to' => 'Purchasing To',
    'receiving_by' => 'Purchasing By',
    'admin' => 'Admin',
    'be_careful_you_are_make_a_new_admin_same_as_you' => 'Be careful, you are making a new admin.',
    'mark_as_admin' => 'Mark as admin',
    'remove_from_admin' => 'Remove from admin',
    'password_successfully_changed' => 'Password changed successfully.',
    'you_are_removing_from_admin' => 'You are removing this user from admin.',
    'something_went_wrong' => 'Something went wrong',
    'done_successfully' => 'Done successfully',
    'user_invitation_successfully_sent' => 'User invitation sent successfully.',
    'something_went_wrong_can_not_send_the_email' => 'Please setup your email first!',
    'payment_done_successfully' => 'Payment done successfully.',
    'user_role_successfully_changed' => 'User role changed successfully.',
    'user_successfully_enabled' => 'User enabled successfully.',
    'user_successfully_disabled' => 'User disabled successfully.',
    'user_successfully_marked_as_admin' => 'User successfully marked as admin.',
    'user_successfully_removed_from_the_role_as_admin' => 'User successfully removed from the role as admin',
    'select_cash_register' => 'Select Cash Register',
    'select_sales_or_receiving_type' => 'Select Sales or Receiving Type',
    'select_sales_or_returns_type' => 'Select Sales or Returns Type',
    'select_purchase_or_returns_type' => 'Select Purchase or Returns Type',
    'register_info' => 'Register Info',
    'total_cash_sales' => 'Total cash sales',
    'total_cash_purchase' => 'Total cash purchase',
    'total_purchases' => 'Total purchase',
    'offline' => 'OFFLINE',
    'online' => 'ONLINE',
    // 'contacts' => 'Contacts',
    'contacts' => 'Students',
    'can_see_contacts' => 'Can see contacts',
    'can_not_delete' => 'Can not delete',
    'units' => 'Units',
    'short_name' => 'Short Name',
    'short_name_input' => 'Short name',
    'use_cash_register' => 'Use cash register',
    'enable_shipment' => 'Enable shipment',
    'user_name' => 'User',
    'import' => 'Import',
    'export_sales_report' => 'Export sales report',
    'choose_xlsx_file_only' => 'Choose .xlsx files only',
    'sample' => 'Sample',
    'successfully_imported_from_your_file' => 'successfully imported from your file.',
    'preview' => 'Preview',
    'product_preview' => 'Product preview',
    'uploading' => 'Uploading........',
    'select_a_file_first_to_preview' => 'Select a file first to preview!',
    'incorrect_column_name' => 'Incorrect column name.',
    'auto_generate_invoice' => 'Auto Generate Invoice',
    'sales_chart' => 'Sales Chart',
    'sales_statistics' => 'Sales Statistics',
    'stock_chart' => 'Stock Chart',
    'available_stock' => 'Available Stock',
    'year' => 'Year',
    'choose_date' => 'Choose date',
    'choose' => 'Choose',
    'mailgun' => 'Mailgun',
    'mandrill' => 'Mandrill',
    'sparkpost' => 'Sparkpost',
    'mailgun_domain' => 'Mailgun domain',
    'mailgun_api' => 'Mailgun API',
    'mandrill_api' => 'Mandrill API',
    'sparkpost_api' => 'Sparkpost API',
    'column_name_does_not_match_with_the_sample' => 'Column name does not match with the sample',
    'upload_a_file_first_to_preview' => 'Upload a file first to preview!',
    'load_more' => 'Load more',
    'opening_stock' => 'Opening Stock', 
    'upload_file_to_import' => 'Upload file to import',
    'settings_saved_successfully' => 'Setting saved successfully.',
    'does_not_match_with_any_product' => 'does not match with any product',
    'deleted_successfully' => 'deleted successfully.',
    'variant_name' => 'Variant Name',
    'but_email_already_exists' => 'but email already exists',
    'walk_in_customer' => 'Walk-in customer',
    'walk_in_supplier' => 'Unspecified supplier',
    'time_period' => 'Time period',
    'sku_or_barcode_or_title_does_not_already_exists' => 'SKU or Barcode or Title does not already exists',
    'invalid_data_download_file_to_see_the_error' => 'Invalid data. Download file to see the error.',
    'password_is_required' => 'Password is required',
    'confirm_password_is_required' => 'Confirm Password is required',
    'password_must_be_at_least_6_characters' => 'Password must be at least 6 characters',
    'passwords_must_match' => 'Passwords must match',
    'product_settings' => 'Product Settings',
    'field_should_not_empty' => 'Field should not empty',
    'file_should_not_empty' => 'File should not be empty',
    'download_error_file' => 'Download error file',
    'non_existing_product' => 'Non existing product',
    'duplicate_in' => 'Duplicate',
    'invalid_data' => 'INVALID DATA',
    'field_must_not_be_empty' => 'Field must not be empty',
    'this_field_is_required' => 'This field is required',
    'back_page' => 'Back',
    'can_manage_sales_price' => 'Can manage sales price',
    'download_sample_file' => 'Download sample',
    'allow_multi_user' => 'Allow multi user',
    'join' => 'Join',
    'product_units' => 'Product Units',
    'email' => 'Email',
    'invoices' => 'Invoices',
    'sales_invoices' => 'Sales Invoices',
    'stock_adjustment_invoices' => 'Stock Adjustment Invoices',
    'purchase_invoices' => 'Purchase Invoices',
    'product_type_must_be_either_standard_or_variant' => 'Product type must be either standard or variant',
    'add_new_variant' => 'Add new variant',
    'duplicate_barcode' => 'Duplicate barcode',
    'duplicate_sku' => 'Duplicate sku',
    'no_tax' => ' No tax',
    'default_tax' => 'Default tax',
    'discount_all_items_by_percent' => 'Discount on all items',
    'discount_on_sub_total' => 'Discount on subtotal',
    'please_close_the_current_cash_register_to_continue' => 'Please close the current cash register to continue',
    'this_email_are_match_in_previous_database_email' => 'This Email Are Match In Previous Database Email',
    'cancel_card_item' => 'Cancel card item',
    'cash_opening_balance' => 'Cash opening balance',

    //Shortcuts
    'keyboard_shortcut_settings' => 'Keyboard shortcut settings',
    'sales_shortcuts' => 'Sales Shortcuts',
    'product_search' => 'Product search',
    'hold_card' => 'Hold card',
    'keyboard_shortcut_settings_updated_successfully' => 'Keyboard shortcut settings updated successfully.',
    'keyboard_shortcut_settings_added_successfully' => 'Keyboard shortcut settings added successfully.',
    'load_sales_page' => 'Load sales page',
    'shortcut_key_must_be_unique' => 'Shortcut key must be unique.',
    'enable_shortcut' => 'Enable shortcut',
    'go_to_sales_page' => 'Go to sales page',
    'hold_cart' => 'Hold cart',
    'make_payment' => 'Make payment',
    'cancel_order' => 'Cancel order',
    'you_can_change_the_shortcut_settings_from_here' => 'You can change the shortcut settings from here',
    'shortcut_setting_information' => 'You can combine shortcut with \'shift\' and \'ctrl\' key. Or you can use any single key from \'a - z\'',

    //Shipping area setting
    'shipping_area' => 'Shipping area',
    'add_shipping' => 'Add shipping item',
    'edit_shipping' => 'Edit shipping item',
    'shipping' => 'Shipping',
    'area_name' => 'Area name',
    'area' => 'Area',
    'address' => 'Address',
    'shipping_item' => 'Shipping item',
    'shipping_info_deleted_permanently' => 'This shipping area will be deleted permanently.',
    'can_manage_shipping_area' => 'Can manage shipping area',
    'can_see_shipping_area' => 'Can see shipping area',
    'shipping_status' => 'Shipping Status',
    'shipping_cost' => 'Shipping Cost',
    'and_shipping_status' => 'and shipping status',
    'shipment_status_packet' => 'Packet',
    'shipment_status_on_the_way' => 'On the way',
    'shipment_status_delivered' => 'Delivered',
    'can_see_shipment_report' => 'Can see shipment report',
    'shipment_status_has_ben_update_successfully' => 'Shipment status has ben update successfully',

    //invoice
    'invoice' => 'INVOICE',

    //barcode
    'print' => 'Print',
    'print_barcode' => 'Print Barcode',
    'choose_product' => 'Choose Product',
    'product_name' => 'Product Name',
    'product_variant' => 'Product Variation',
    'product_price' => 'Product Price',
    'barcode_size' => 'Barcode Size',
    'barcode_preview' => 'Barcode Preview',
    'modal_close' => 'Close',
    'variant_title' => 'Variant Title',
    'product_size' => 'Size',
    'select_how_many_columns' => 'Select How Many Columns',
    'select_barcode_size' => 'Select Barcode Size',
    'select_how_many_copies' => 'Select How Many Copies',
    'select_how_many_rows_in_every_pages' => 'Select How Many Rows In Every Pages',
    'product_settings_saved_successfully' => 'Product settings saved successfully.',
    'disable' => 'Disable',
    'enable' => 'Enable',
    'sku_already_exists' => 'SKU already exists',
    'barcode_already_exists' => 'Barcode already exists',
    'include_application_name' => 'Include application name',
    'advance_option' => 'Advance option',
    'barcode_height' => 'Barcode height (mm)',

    //Card payment
    'card_transaction_details' => 'Card Transaction Details',
    'card_number' => 'Card Number',
    'card_holder_name' => 'Card Holder Name',
    'card_transaction_no' => 'Card Transaction No',
    'card_type' => 'Card Type',
    'master_card' => 'Master card',
    'visa_card' => 'Visa card',
    'security_code' => 'Security Code',
    'month' => 'Month',
    'choose_month' => 'Choose month',

    //Bank Transfer
    'bank_transfer_details' => 'Bank Transfer Details',
    'bank_account_number' => 'Bank Account Number',

    //Import xlxs files
    'required_field_is_empty' => 'Required field is empty.',
    'required_field' => 'Required',
    'duplicate_email_not_allowed' => 'Duplicate email not allowed.',
    'and' => ' And ',
    'checking_for_updates' => 'Checking for new updates...',
    'installing_version' => 'Installing version',
    'invalid_purchase_code' => 'Invalid purchase code',
    'no_updates_found' => 'No updates found',
    'install_zip_extension' => 'Please install zip extension in your server!',
    'please_install_version' => 'Please install version ', 'first' => ' first!',
    'version' => 'Version ', 'installed_successfully' => ' installed successfully!',
    'no_data_found' => 'No data found',
    'current_version' => 'Current Version',
    'available_updates' => 'Available Updates',
    'click_to_install_version' => 'Click to install version',
    'backup_reminder' => 'Please backup your application files and database before installing a new version!',
    'standard' => 'Standard',
    'can_see_users_details' => 'Can see users details',
    'can_see_adjust_stock' => 'Can see adjust stock reports',
    'can_see_customer_details' => 'Can see customers details',
    'can_see_supplier_details' => 'Can see suppliers details',
    'email_sent_and_settings_saved_successfully' => 'Email sent and settings saved successfully.',
    // 'cash_receives' => 'Cash Receives',
    'cash_receives' => 'Purchases',
    'expected_closing_amount' => 'Expected closing amount',

    //Month Name Long Form
    'january' => 'January',
    'february' => 'February',
    'march' => 'March',
    'april' => 'April',
    'may' => 'May',
    'june' => 'June',
    'july' => 'July',
    'august' => 'August',
    'september' => 'September',
    'october' => 'October',
    'november' => 'November',
    'december' => 'December',

    //Month Name Short Form * used in dashboard bar chart *
    //May month is included in 'Month Name Long Form'
    'jan' => 'Jan',
    'feb' => 'Feb',
    'mar' => 'Mar',
    'apr' => 'Apr',
    'jun' => 'Jun',
    'jul' => 'Jul',
    'aug' => 'Aug',
    'sep' => 'Sep',
    'oct' => 'Oct',
    'nov' => 'Nov',
    'dec' => 'Dec',

    //Week Name Short Form
    'sun' => 'Sun',
    'mon' => 'Mon',
    'tue' => 'Tue',
    'wed' => 'Wed',
    'thu' => 'Thu',
    'fri' => 'Fri',
    'sat' => 'Sat',

    'search_product' => 'Search Product',
    'search_orders' => 'Search Orders',
    'search_customers' => 'Search Customers',
    'search_branch' => 'Search Branch',
    'search_suppliers' => 'Search Suppliers',
    'add_invoice_template' => 'Add Invoice Template',
    'edit_invoice_template' => 'Edit Invoice Template',
    'available_key' => 'Available key',
    'invoice_setting_saved_successfully' => 'Invoice setting save successfully',
    'customer_invoice' => 'Customer Invoice',
    'sales_receipt' => 'Sales Receipt',
    'customer_groups' => 'Customer Groups',
    'customer_groups_dt' => 'Customer groups',
    'sync_complete_your_all_sales_now_up_to_date' => 'Sync complete! Your all sales now up to date.',
    'problem_occurred_while_transferring_data_from_offline_to_online_mode' => 'Problem occurred while transferring data from offline to online mode',
    'your_local_storage_is_full' => 'Your local storage is full!',
    'are_you_want_to_increase_your_local_storage' => 'Are you want to increase your local storage?',
    'offline_mode' => 'Offline sales',
    'you_can_not_enable_offline' => 'You can not enable offline if you have enabled multi user access in Cash Registers settings!',
    'you_cant_allow_to_use_multi_use_in_same_cash_register_if_you_use_the_offline_sales' => 'You can’t allow to use multi use in same cash register if you use the offline sales.',
    'you_can_not_allow_multi_user' => 'You can not allow multi user if you have enabled offline mode in App settings!',

    // Restaurant Module
    'order_type' => 'Order Type',
    'dine_in' => 'Dine In',
    'take_away' => 'Take Away',
    'search_invoice' => 'Search Invoice',
    'no_match_found_with_this_Invoice' => 'No match found with this Invoice',
    'place_order' => 'Place Order',
    'sales_sms' => 'Sales sms',
    'pos_sms' => 'POS sms',
    'customer_welcome_sms' => 'Customer welcome sms',
    'new_customer_welcome_sms' => 'New customer welcome sms',
    'proceed_order' => 'Proceed Order',
    'print_order' => 'Print Order',
    'select_table' => 'Select Table',
    'details' => 'Details',
    'receipt' => 'Receipt',
    'table_deleted_permanently' => 'This table will be deleted permanently.',
    'booked' => 'booked',
    'available' => 'available',
    'phone_number_wrong' => 'Phone number wrong',
    'successfully_sms' => 'Sms send successfully',

    // List To do
    'todo' => 'To-do',
    'todo_list' => 'Todo List',
    'task_list' => 'Task List',
    'sort_by' => 'Sort By',
    'due_date' => 'Due date',
    'most_recent' => 'Most Recent',
    'add_new_task' => 'Add New Task',
    'add_due_date' => 'Add due date',
    'no_incomplete_task_found' => 'No incomplete task found',
    'show_completed_task' => 'Show Completed Tasks',
    'hide_completed_task' => 'Hide Completed Tasks',

    // Adjust stock
    'adjust_stock' => 'Stock Adjustment Type',
    'add_adjust_stock' => 'Add Stock Adjustment Type',
    'edit_adjust_stock' => 'Edit Stock Adjustment Type',
    'adjust_stock_type' => 'Stock Adjustment type',
    'adjust_stock_details' => 'Stock Adjustment details',
    'this_adjust_stock_deleted_permanently' => 'This stock adjustment type will be deleted permanently',
    'adjustment_type' => 'Adjustment Type',
    'adjustment_type_label' => 'Adjustment type',
    'adjustment_item' => 'Adjustment Item',
    'default_variant' => 'Default Variant',
    'stock_adjustment' => 'Stock Adjustment',
    'stock_adjustment_req' => 'Stock Adjustment Requests ',
    'adjust_stock_successfully' => 'Stock adjustment successfully.',
    'stock_adjustment_instruction' => 'If you set a positive quantity, that will be added in your stock and if you set negative quantity that will be minus from the stock',
    'sales_settings' => 'Sales Setting',
    'out_of_stock_products_mode' => 'Disable sales for out of stock products',
    'sales_setting_update_successfully' => 'Sales setting Update Successfully',
    'copyright_text' => 'Copyright @ 2020 by Gain POS',
    'transfer' => 'Transfer',
    'chose_one' => 'Choose one',
    'tin_number' => 'TIN ( Tax Identification Number )',
    'sales_note' => 'Sales note',
    'send_sms_supplier' => 'Send sms supplier',
    'required_permission_and_server_requirements_is_complete' => 'Required permission and server requirements is not fulfilled',

    //Installer
    'database_configuration' => 'Database Configuration',
    'database_connection' => 'Database connection',
    'enter_database_connection' => 'Enter database connection',
    'database_hostname' => 'Database hostname',
    'database_port' => 'Database port',
    'database_name' => 'Database name',
    'database_username' => 'Database username',
    'database_password' => 'Database password',
    'purchase_code' => 'Purchase Code',
    'code' => 'Code',
    'admin_login_details' => 'Admin Login Details',
    'install' => 'Install',
    'your' => 'your',
    'app_installed_successfully' => 'App installed successfully',
    'mysql' => 'MySQL',
    'pgsql' => 'PostgreSQL',
    'sqlsrv' => 'SQL Server',
    'enter_database_hostname' => 'Enter database hostname',
    'enter_database_port' => 'Enter database port',
    'enter_database_name' => 'Enter database name',
    'enter_database_username' => 'Enter database username',
    'enter_database_password' => 'Enter database password',
    'enter_full_name' => 'Enter full name',
    'enter_code' => 'Enter code',
    'installer_email' => 'Email',
    'installer_password' => 'Password',
    'database_credential_error' => 'Incorrect credential of Database!',
    'install_gain_pos' => 'Install Gain POS',
    'save_and_next' => 'Save & Next',

    //1.5.4
    'selected' => 'Selected',
    'large' => 'Large',
    'small' => 'Small',
    'width' => 'Width',
    'invoice_size' => 'Invoice size',
    'supplier_name' => 'Supplier Name',
    'discount_adjustment' => 'Discount adjustment',
    'invoice_size_change_warning' => 'Changing invoice size will remove all the template changes',
    'content_of_the_invoice_will_be_restored_to_new_default_content_of_app_version' => 'Invoice contents will be replaced with newest content for the app version 1.6 or later.',
    'independent_chips' => 'Independent chips',
    'table_chips' => 'Table chips',
    'warning' => 'Warning!',
    'export_all' => 'Export all',

    'barcode_prefix' => 'Barcode Prefix',
    'product_category_label' => 'Product Categories',
    'category_sku_prefix' => 'Category SKU Prefix',
    'category_barcode_prefix' => 'Category Barcode Prefix',
    'auto_generate_sku' => 'Auto Generate SKU',
    'auto_generate_barcode' => 'Auto Generate Barcode',
    'barcode_start_from' => 'Barcode Start From',
    'last_barcode' => 'Last Barcode',
    'sku_start_from' => 'SKU Start From',
    'last_sku' => 'Last SKU',
    'add_on' => 'AddOns',
    'custom_add_on' => 'Add Custom AddOn',
    'pre_order' => "Pre Order",
    'pre-orders' => "Pre Orders",
    'advance' => "Advance",
    "weight" => "Weight",
    "wgt" => "Wgt",

    //Inventory
    'supplier_deleted_permanently' => 'This supplier will be deleted permanently.',
    'unit_deleted_permanently' => 'This unit will be deleted permanently.',
    'material_deleted_permanently' => 'This raw material will be deleted permanently.',
    'inventory_deleted_permanently' => 'This inventory will be deleted permanently.',
    'image_deleted_permanently' => 'This image will be deleted permanently.',
    'inventory_submit' => 'The inventory is not editable after saving. Please check it before submission. Additionally, records are not being deleted.',
    'usage_deleted_permanently' => 'This usage will be deleted permanently.',
    'usage_submit' => 'The usage is not editable after saving. Please check it before submission. Additionally, records are not being deleted.',
    'nothing_to_update' => 'Nothing to Update!',
    'raw_data' => 'Raw Material',
    'raw_data_opn_stock' => 'Raw Material Opening Stock',
    'open_stock' => "Open Stock",
    'add_raw_data' => 'Add New Material',
    'add_raw_data_label' => 'Add new Item Material',
    'edit_raw_data' => 'Edit Material',
    'purchase_order_id' => 'Purchased Order ID',
    'ava_qty' => 'Available Quantity',
    'usage_qty' => 'Usage Quantity',
    'enter_qty' => 'Required Quantity',
    'remarks' => 'Remarks',
    'kitchen' => 'Kitchen',
    'bakery' => 'Bakery',
    'wastage' => 'Damage / Expired',
    'central_store' => 'Central Store',
    'usage' => 'Usage',
    'inventory_stock' => 'Inventory Stock',
    'unit_name' => 'Unit Name',
    'upload_bill_image' => 'Upload Bill Files',
    'files_uploaded' => 'Files are Uploaded Successfully.',
    'purchase_report' => 'Purchases',
    'supplier_type' => 'Supplier Type',
    'usage_report' => 'Usage Details',
    'items_wise_report' => 'Purchases Details',
    'purchase_qty' => 'Purchase Quantity',
    'sales_users' => 'Users',

    //mbupos
    'course' => 'Course',
    'combo' => 'Combo',
    'courses' => 'Courses',
    'combos' => 'Combos',
    'add_course' => 'Add New Course',
    'edit_course' => 'Edit Course',
    'add_combo' => 'Add Combo',
    'course_name' => 'Course Name',

    'packages' => 'Packages',
    'add_package' => "Add New Package",
    'edit_package' => "Edit Package",
    "package_name" => "Package Name",
    'package' => 'Package',

    'package_for' => 'Package For',
    'purchase_order' => 'Purchase Order',
    'purchase_grn' => 'GRN',
    'select_purchase_order_or_grn' => 'Select Purchase Order or GRN ',
    'roll_no' => "Roll No.",


    'profiles' => "Profiles",
    'profile_groups' => "Profile Groups",
    'profile_group' => "Profile Group",
    'students_staff' => 'Students / Staff',
    'add_student_staff' => 'Add Student / Staff',
    'edit_student_staff' => 'Edit Student / Staff',
    'aadhar_no' => 'Aadhaar Number',
    'school_college' => 'School or College',
    'generate_purchase_order' => 'Generate Purchase Order',
    'print_po' => "Print PO",
    'send_po_supplier' => "Send PO to Supplier",
    'purchase_edit_order' => "Purchase Edit Order",
    'update_purchase_order' => "Update Purchase Order",
    'generate_grn' => "Generate GRN",
    'print_gnr' => "Print GRN",
    'uniform_fee' => "Uniform fee",
    'uniform_paid' => "Uniform paid",
    'uniform_balance' => "Uniform balance",
    'refno' => 'Ref. No.' ,
    'grn_order_status' => 'GRN Status',
    'can_manage_quotations' => 'Can Manage Quotations',
    'can_accept_approvals' => 'Can Accept Approvals',
    'quotation_created_success' => 'Quotation Created with Suppliers',
    'request_approval_order' => 'Request Approval Order',
    'approval_list' => 'Approvals List',
    'roll_number' => 'Roll Number',
    'can_edit_payment_type' => 'Can manage Invoice Payment Method Type',
    'can_ask_customer_popup' => 'Can Ask Cusomer Add Popup Before Pay in Sales',
    'grn_invoice_settings' => 'GRN Invoice Settings',
    'grn_invoice' => 'GRN Invoices',
    'grn_invoice_id' => 'GRN Invoice ID',
    'items_received' => 'Item Received',
    'grn' => 'GRN',
    'recevied' => 'Receiving',
    'grn_invoice_prefix' => 'GRN Invoice prefix',
    'grn_invoice_starts_from' => 'GRN Invoice starts from',

    'can_see_grn_details_report' => 'Can See GRN Details Report',
    'can_see_purchase_details_report' => 'Can See Purchase Details Report',
    'can_see_stock_movement_register_report' => 'Can See Stock Movement Register Report',
    'stock_movement_register' => 'Stock Movement Register',
    'grn_details' => 'GRN Details',
    'purchase_details' => 'Purchase Details',
    'purchase_invoice_id' => 'PO Invoice ID',
    'customer_combo_report' => "Student Pending Combo Report",
    'can_see_customer_combo_report' => "Can See Student Pending Combo Report",
    'pending_quantity'    => "Pending Qty",
    'issued_quantity'    => "Issued Qty",
    'qty' => 'Qty',
    'only_pending_quantity'    => "Only Pending Qty",
    'only_issued_quantity'    => "Only Issued Qty",
    'stock_report' => "Stock Report",
    'balance_should_be' => 'Balance Should Be',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'cancelled' => 'Cancelled',
    'deleted' => 'Deleted',
    'individual_sale' => 'Individual Sale',
    'combo_sale' => 'Combo Sale',
    'return_sales' => 'Return Sales',
    'can_see_stock_report' => 'Can See Stock Report',
    'created_dt' => "Created Dt",
    'approved_at' => "Approved Dt",
    'can_see_stock_adjustment_req' => 'Can see Stock Adjustment Requests',
    'can_manage_stock_adjustment_req' => 'Can manage Stock Adjustment Requests',

    //Add the new parameters in the Sales payment screen.
    'upload_payment_reference' => 'Payment Reference',
    'payment_reference' => 'Payment Reference',
    'payment_reference_error' => 'Choose file Image/PDF format!',
    'payment_file_size' => 'File size should be less than 1MB',
    'payment_file_format' => 'Only Image and PDF format files are allowed.',
    'view' => 'View',
);