<?php

namespace App\Exports;

use App\Libraries\AllSettingFormat;
use App\Models\OrderItems;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class SalesSummaryReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {

        return  OrderItems::query()->select(
            'order_items.tax_id',
            DB::raw('abs(sum(order_items.quantity*order_items.price-(order_items.quantity*order_items.price*order_items.discount)/100)) as sub_total'),
            DB::raw('abs(sum((order_items.quantity*order_items.price -(order_items.quantity*order_items.price*order_items.discount)/100)*taxes.percentage/100)) as tax'),
            DB::raw("(sum((abs(order_items.quantity)*order_items.price)* order_items.discount)/100) AS discount"),
            DB::raw('CONVERT(abs(sum(order_items.quantity)),SIGNED INTEGER) as item_purchased'),
            'product_brands.name'
        )
            ->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->leftJoin('taxes', 'taxes.id', '=', 'order_items.tax_id')
            ->join('products', 'products.id', '=', 'order_items.product_id')
            ->leftJoin('product_brands', 'product_brands.id', '=', 'products.brand_id')
            ->where('orders.order_type', 'sales')
            ->where('orders.status', 'done')
            ->groupBy('products.brand_id');
    }

    public function map($reportRow): array
    {       
        $sub_total = number_format((float)$reportRow->sub_total, 2, '.', '');
        $tax =  number_format((float)$reportRow->tax, 2, '.', '');
        $total = $sub_total+$tax;
        return 
            [
                $reportRow->name,
                $reportRow->item_purchased,
                number_format((float)$reportRow->sub_total, 2, '.', ''),
                number_format((float)$reportRow->tax, 2, '.', ''),
                number_format((float)$reportRow->discount, 2, '.', ''),
                $total,
            ];                    
    }

    public function headings(): array
    {
        return [
           "Brand", "Item Sold", "Sub Total", "Tax", "Discount", "Total"
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {

        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->getStyle('A1:F1')->applyFromArray([
                        'font' =>[
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' =>[
                                'color' => ['argb' =>'FFFF0000']
                            ],
                        ]
                    ],
                );
                
                
                $query_result = $this->query()->get();
                $rows_count = count($query_result);
                
                $item_sold  = $query_result->sum('item_purchased');  
                $discount  = $query_result->sum('discount');  
                $tax  = $query_result->sum('tax');  
                $sub_total = $query_result->sum('sub_total');
                $total = $tax+$sub_total;
                $rows_count = $rows_count + 3;

                $allSettingFormat = new AllSettingFormat;

                $event->sheet->setCellValue('A'.$rows_count, 'Grand Total'); 
                $event->sheet->setCellValue('B'.$rows_count, $item_sold); 
                $event->sheet->setCellValue('C'.$rows_count, $allSettingFormat->getCurrencySeparator($sub_total));
                $event->sheet->setCellValue('D'.$rows_count, $allSettingFormat->getCurrencySeparator($tax));
                $event->sheet->setCellValue('E'.$rows_count, $allSettingFormat->getCurrencySeparator($discount));
                $event->sheet->setCellValue('F'.$rows_count, $allSettingFormat->getCurrencySeparator($total));
                
            },
        ];
    }
    
}
