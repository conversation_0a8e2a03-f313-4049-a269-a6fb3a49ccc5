<?php

namespace App\Exports;

use App\Libraries\AllSettingFormat;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class SuppliersSummaryReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {
        return Order::where('orders.order_type', 'receiving')
        ->leftJoin('suppliers', 'suppliers.id', 'orders.supplier_id')
        ->where('orders.type', 'supplier')
        ->groupBy('orders.supplier_id')
        ->select('orders.supplier_id',
            DB::raw('CONCAT(suppliers.first_name," ",suppliers.last_name, "\n", suppliers.company )  AS name'),
            DB::raw('abs(sum(orders.due_amount)) as due'),
            DB::raw('abs(sum(orders.total)) as total_purchase'),
            DB::raw('abs(sum(total - due_amount)) as total_payment')
        )->orderBy('name', 'asc');
    }


    public function map($reportRow): array
    {
        $allSettingFormat = new AllSettingFormat;
        return
            [
                $reportRow->name,
                $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->total_purchase, 2, '.', '')),
                $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->total_payment, 2, '.', '')),
                $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->due, 2, '.', '')),
            ];
    }

    public function headings(): array
    {
        return [
            "Supplier",
            "Suppliers Purchase",
            "Total Payment",
            "Due"
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:D1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );


                $query_result = $this->query()->get();

                $rows_count = count($query_result);

                $total_purchase = $query_result->sum('total_purchase');
                $total_payment = $query_result->sum('total_payment');
                $due = $query_result->sum('due');

                $rows_count = $rows_count + 3;

                $allSettingFormat = new AllSettingFormat;
                $event->sheet->setCellValue('A' . $rows_count, 'Grand Total');
                $event->sheet->setCellValue('B' . $rows_count, $allSettingFormat->getCurrencySeparator($total_purchase));
                $event->sheet->setCellValue('C' . $rows_count, $allSettingFormat->getCurrencySeparator($total_payment));
                $event->sheet->setCellValue('D' . $rows_count, $allSettingFormat->getCurrencySeparator($due));


            },
        ];
    }

}
