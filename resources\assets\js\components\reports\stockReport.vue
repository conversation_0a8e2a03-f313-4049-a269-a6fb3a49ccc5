<template>
    <div>
        <span v-if="!hasData(tableOptions)">
            <pre-loader></pre-loader>
        </span>
        <span v-else>
            <!--Export Button-->
            <div class="main-layout-card-header-with-button">
                <div class="main-layout-card-content-wrapper">
                    <div class="main-layout-card-header-contents">
                        <h5 class="m-0">{{ trans('lang.stock_report') }}</h5>
                    </div>
                    <div class="main-layout-card-header-contents text-right">
                        <button class="btn btn-secondary app-color"
                                @click.prevent="exportAll('/export/all/report/stock-report')" :disabled="isDisabled"  >
                            {{ trans('lang.export_all') }}
                        </button>                         
                    </div>
                </div>
            </div>
            <datatable-component
                class="main-layout-card-content"
                :options="tableOptions"
                :exportData="exportToVue"
                :tab_name="tabName"
                :route_name="routeName"
                exportFileName="adjust_stock"
                @resetStatus="resetExportValue"
                @filtersData="filtersDataFunciotn"
                @searchValue="searchValueFunction"
            ></datatable-component>

            <!-- Modal -->
            <div
                class="modal fade"
                id="due-amount-edit-modal"
                tabindex="-1"
                role="dialog"
                aria-hidden="true"
            >
                <div class="modal-dialog modal-dialog-centered short-modal-dialog" role="document">
                    <cart-due-payment
                        class="modal-content"
                        v-if="isActive"
                        :rowdata="selectedItemId"
                        :orderType="order_type"
                        :modalID="modalID"
                        :modalTitle="trans('lang.due_total')"
                        @cartItemsToStore="cartItemsToStore"
                    ></cart-due-payment>
                </div>
            </div>
        </span>
    </div>
</template>
<script>
import axiosGetPost from "../../helper/axiosGetPostCommon";
export default {
    props: ["permission"],
    extends: axiosGetPost,
    data() {
        return {
            isActive: false,
            isActiveAttributeModal: false,
            selectedItemId: "",
            order_type: "sales",
            hidePreLoader: false,
            exportToVue: false,
            buttonLoader: false,
            isDisabled: false,
            isActiveText: false,
            tabName: "adjust_stock_report",
            routeName: "reports",
            tableOptions: {},
            filtersDataOptions:[],
            searchValueOptions:'',
            hasData: value => {
                return !_.isEmpty(value);
            }
        };
    },
    created() {
        this.getSalesReport();
    },
    mounted() {
        let instance = this;

        this.modalCloseAction(this.modalID);

        $("#attributes-add-edit-modal").on("hidden.bs.modal", function(e) {
            instance.isActiveAttributeModal = false;
            $("body").addClass("modal-open");
        });

        this.$hub.$on("viewSalesReportEdit", function(rowdata) {
            instance.addEditAction(rowdata);
        });
    },
    methods: {
        getSalesReport() {
            let instance = this;
            instance.axiosGet(
                "/adjustment-report-filter",
                function(response) {
                    if (response.data) {
                        /*Appending cash register static value(All) with dynamic cash register title from db*/
                        let branches = [
                                { text: "All", value: "all", selected: true },
                                ...response.data.branches
                            ],
                            products = [
                                { text: "All", value: "all", selected: true },
                                ...response.data.products
                            ],
                            adjustmentTypes = [
                                { text: "All", value: "all", selected: true },
                                ...response.data.adjustmentTypes
                            ],
                            brands = [{text: 'All', value: 'all', selected: true}, ...response.data.brandName],
                            categories = [{text: 'All', value: 'all', selected: true}, ...response.data.categoryName],
                            groups = [{text: 'All', value: 'all', selected: true}, ...response.data.groupName]
                            ;


                        instance.tableOptions = {
                            tableName: "products",
                            columns: [
                                
                                {title: 'lang.category_name', key: 'category_name', type: 'text', sortable: true},
                                {title: 'lang.group_name', key: 'group_name', type: 'text', sortable: true},
                                {title: 'lang.brand_name', key: 'brand_name', type: 'text', sortable: true},
                                {
                                    title: "lang.product_name",
                                    key: "title",
                                    type: "text",
                                    sortable: false
                                },
                                {
                                    title: "lang.product_size",
                                    key: "variant_title",
                                    type: "text",
                                    sortable: false
                                }, 
                                {
                                    title: "lang.opening_stock",
                                    key: "opening_qty",
                                    type: "text",
                                    sortable: false
                                },
                                {
                                    title: "Opening Stock Value",
                                    key: "opening_value",
                                    type: "text",
                                    sortable: false
                                },
                                {
                                    title: "lang.purchase_report",
                                    key: "purchase_qty",
                                    type: "text",
                                    sortable: false
                                },
                                {
                                    title: "Purchase Value",
                                    key: "purchase_value",
                                    type: "text",
                                    sortable: false
                                },
                                {
                                    title: "lang.sales",
                                    key: "sale_qty",
                                    type: "text",
                                    sortable: false
                                },
                                {
                                    title: "Sales Value",
                                    key: "sale_value",
                                    type: "text",
                                    sortable: false
                                },
                                {
                                    title: "lang.inventory",
                                    key: "inventory_qty",
                                    type: "text",
                                    sortable: false
                                },
                                {
                                    title: "Inventory Value",
                                    key: "inventory_value",
                                    type: "text",
                                    sortable: false
                                },
                                 
                            ],
                            source: "/stock-report",
                            summary: false,
                            search: true,
                            sortedBy: "",
                            sortedType: "DESC",
                            formatting: [], 
                            summation: [],
                            summationKey: [],                            
                            center_align: ['sale_qty', 'inventory_qty', 'purchase_qty', 'opening_qty', 'opening_value', 'purchase_value', 'sale_value', 'inventory_value'],
                            filters: [
                                {
                                    title: "lang.date_range",
                                    key: "date_range",
                                    type: "date_range"
                                },
                                
                                
                                {
                                    title: "lang.product",
                                    key: "product_name",
                                    type: "dropdown",
                                    languageType: "raw",
                                    options: products
                                },

                                {
                                    title: 'lang.brand',
                                    key: 'brand_name',
                                    type: 'dropdown',
                                    languageType: "raw",
                                    options: brands
                                },
                                {
                                    title: 'lang.category',
                                    key: 'category_name',
                                    type: 'dropdown',
                                    languageType: "raw",
                                    options: categories
                                },
                                {
                                    title: 'lang.group',
                                    key: 'group_name',
                                    type: 'dropdown',
                                    languageType: "raw",
                                    options: groups
                                }
 
                                 
                            ]
                        };
                    }
                    instance.setPreLoader(true);
                },
                function(response) {
                    instance.setPreLoader(true);
                }
            );
        },
       
        getActiveAttributeModal(isActive) {
            this.isActiveAttributeModal = isActive;
        },
        exportStatus() {
            this.exportToVue = true;
            this.buttonLoader = true;
            this.isDisabled = true;
        },
        resetExportValue(value) {
            this.exportToVue = value;
            this.buttonLoader = false;
            this.isDisabled = false;
        },
        filtersDataFunciotn(value){            
            this.filtersDataOptions = value;
        },
        searchValueFunction(value){
            this.searchValueOptions = value;
        },
        exportAll(url) {
            this.isDisabled = true;
                axios.post(url, {
                    export: true,
                    filtersData : this.filtersDataOptions,
                    searchValue:this.searchValueOptions
                }, {
                    responseType: 'blob'
                }).then(response => {
                    this.isDisabled = false;
                    const url = window.URL.createObjectURL(new Blob([response.data]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', 'stock-report.xlsx'); 
                    document.body.appendChild(link);
                    link.click();
                }).catch(error => {
                    this.isDisabled = false;
                    console.error(error);
                });
            }
    }
};
</script>