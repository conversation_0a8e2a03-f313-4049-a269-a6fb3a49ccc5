<?php

namespace App\Models;
use DB;

class ProductPackage extends BaseModel
{
    protected $table = 'product_packages';
    protected $fillable = ['package_name', 'package_for', 'created_by', 'course_id'];
 
    
    public static function getPackagesData($request)
    {
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $rowLimit = $request->rowLimit;
        $offset = $request->rowOffset;
        $requestType = $request->reqType;

        if (empty($requestType)) {
            /*
            $data = ProductPackage::
                    joinSub(
                        ProductPackage::select('package_id')
                            ->selectRaw('GROUP_CONCAT(products.title) as title')
                            ->join('product_package_products', 'product_packages.id', '=', 'product_package_products.package_id')
                            ->join('products', 'products.id', '=', 'product_package_products.product_id')
                            ->groupBy('product_package_products.package_id'),
                        'tab',
                        'tab.package_id', '=', 'product_packages.id'
                    )
                    ->join('product_courses', 'product_courses.id', 'product_packages.course_id')
                    ->addSelect(['product_packages.*', 'product_courses.course_name', 'tab.title', 
                    DB::raw("CASE WHEN product_packages.package_for = 'M' THEN 'Male' ELSE 'Female' END as gender") ])
                    ->orderBy($columnName, $request->columnSortedBy)->take($rowLimit)->skip($offset)->get();
             
             */
            $data = DB::table('product_packages')
                    ->select(
                        'product_packages.*',
                        'product_courses.course_name',
                        DB::raw('tab.title'),
                        DB::raw("CASE WHEN product_packages.package_for = 'M' THEN 'Male' ELSE 'Female' END as gender")
                    )
                    ->leftJoin(DB::raw('(select product_package_products.package_id, GROUP_CONCAT(products.title) as title from product_packages inner join product_package_products on product_packages.id = product_package_products.package_id inner join products on products.id = product_package_products.product_id group by product_package_products.package_id) as tab'), function($join) {
                        $join->on('tab.package_id', '=', 'product_packages.id');
                    })
                    ->join('product_courses', 'product_courses.id', '=', 'product_packages.course_id')
                    ->orderBy('product_courses.course_name', 'asc')
                    ->limit($rowLimit)
                    ->offset($offset)
                    ->get();
        }else{
            $data = ProductPackage::
            
            joinSub(
                ProductPackage::select('package_id')
                    ->selectRaw('GROUP_CONCAT(products.title) as title')
                    ->join('product_package_products', 'product_packages.id', '=', 'product_package_products.package_id')
                    ->join('products', 'products.id', '=', 'product_package_products.product_id')
                    ->groupBy('product_package_products.package_id'),
                'tab',
                'tab.package_id', '=', 'product_packages.id'
            )
            ->join('product_courses', 'product_courses.id', 'product_packages.course_id')
            ->addSelect(['product_packages.*', 'product_courses.course_name', 'tab.title'])
            
            ->orderBy($columnName, $request->columnSortedBy)->get();
        }
        $count = ProductPackage::count();

        return ['data' => $data, 'count' => $count];
    }
    public static function idOfExisted($fields, $column, $value)
    {
        return ProductPackage::select($fields)->where($column[0], $value[0])->orWhere($column[1], $value[1])->first();
    }

    
    public static function getPackageDetailsAll($customer){
        
        $courses = ProductCourse::select(
            'product_courses.id',
            'product_courses.course_name',
            'product_packages.package_name',
            DB::raw("GROUP_CONCAT(product_variants.variant_title) AS variant_titles"),
            'product_packages.package_for',
            'product_package_products.product_id',
            'product_package_products.quantity',
            DB::raw("CONCAT(product_groups.name, '- ', products.title, ' (', product_variants.variant_title , ')') AS title"),
            DB::raw("-SUM(order_items.quantity) as used_quantity")
        )
        ->join('product_packages', 'product_courses.id', '=', 'product_packages.course_id')
        ->join('product_package_products', 'product_packages.id', '=', 'product_package_products.package_id')
        ->join('products', 'products.id', '=', 'product_package_products.product_id')
        ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
        ->leftJoin('product_variants', 'product_variants.product_id', '=', 'products.id')
        ->leftJoin('order_items', 'order_items.variant_id', '=', 'product_variants.id')
        ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
        ->where('product_courses.id', $customer['course_id'])
        ->where('product_packages.package_for', $customer['gender_id'])
        ->where('order_items.type', 'sales')
        // ->whereIn('product_variants.id', $variantIds)
        // ->where('order_items.order_id', $orderId)
        ->where('orders.customer_id', $customer['id'])
        ->where('orders.status', 'done')
        
        ->groupBy('products.id')
        ->get();
        return $courses;
    }

    public static function getPackageDetailsNotSale($customer){

        $notInUse = ProductCourse::select( 
            DB::raw("GROUP_CONCAT(products.id) as productIds"), 
        )
        ->join('product_packages', 'product_courses.id', '=', 'product_packages.course_id')
        ->join('product_package_products', 'product_packages.id', '=', 'product_package_products.package_id')
        ->join('products', 'products.id', '=', 'product_package_products.product_id')
        ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
        ->leftJoin('product_variants', 'product_variants.product_id', '=', 'products.id')
        ->leftJoin('order_items', 'order_items.variant_id', '=', 'product_variants.id')
        ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
        ->where('product_courses.id', $customer['course_id'])
        ->where('product_packages.package_for', $customer['gender_id'])
        ->where('order_items.type', 'sales') 
        ->where('orders.customer_id', $customer['id'])        
        ->first();
        
        $productIds = isset($notInUse->productIds) ? $notInUse->productIds : '';
     
        
        $results = [];
        $baseQuery = ProductPackage::join('product_package_products', 'product_packages.id', '=', 'product_package_products.package_id')
            ->join('products', 'products.id', '=', 'product_package_products.product_id')
            ->where('product_packages.course_id', $customer['course_id'])
            ->where('product_packages.package_for', $customer['gender_id']);

        if (!empty($productIds)) {
            $productIdsArray = explode(',', $productIds);
            $baseQuery->whereNotIn('product_package_products.product_id', $productIdsArray);
        }

        $results = $baseQuery->get([
            'product_package_products.product_id',
            'product_package_products.quantity as total_quantity',
            'product_package_products.quantity',
            'products.title',
            DB::raw("'0' as used_quantity")
        ]);

        
        return $results;
    }

    public static function getPackageDetails($customer){
        
        $products = DB::table('product_courses')
        ->join('product_packages', 'product_courses.id', '=', 'product_packages.course_id')
        ->join('product_package_products', 'product_packages.id', '=', 'product_package_products.package_id')
        ->join('products', 'products.id', '=', 'product_package_products.product_id')
        ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
        ->where('product_courses.id', $customer['course_id'])
        ->where('product_packages.package_for', $customer['gender_id'])
        ->select('product_courses.id', 'product_courses.course_name', 'product_packages.package_name', 
                 'product_packages.package_for', 'product_package_products.product_id', 'product_package_products.quantity',
                 DB::raw("CONCAT(product_groups.name, '- ', products.title) AS title"))
        ->get();
        return $products;
    }    

    public static function getPackageDetails1($customer, $productIds){
        
        $products = DB::table('product_courses')
        ->join('product_packages', 'product_courses.id', '=', 'product_packages.course_id')
        ->join('product_package_products', 'product_packages.id', '=', 'product_package_products.package_id')
        ->join('products', 'products.id', '=', 'product_package_products.product_id')
        ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
        ->where('product_courses.id', $customer['course_id'])
        ->where('product_packages.package_for', $customer['gender_id'])
        ->whereNotIn('products.id', $productIds)
        ->select('product_courses.id', 'product_courses.course_name', 'product_packages.package_name', 
                 'product_packages.package_for', 'product_package_products.product_id', 'product_package_products.quantity',
                 DB::raw("CONCAT(product_groups.name, '- ', products.title) AS title"))
        ->get()->toArray();
        return $products;
    }

    public static function getPackageDetails2($customer, $orderId){
        

        $variantsData = "SELECT variant_id from order_items where order_id = $orderId GROUP by product_id; ";
        $varients = \DB::select($variantsData);
        
        $variantIds = array_map(function($varients) {
            return $varients->variant_id;
        }, $varients);

        $courses = ProductCourse::select(
            'product_courses.id',
            'product_courses.course_name',
            'product_packages.package_name',
            DB::raw("GROUP_CONCAT(product_variants.variant_title) AS variant_titles"),
            'product_packages.package_for',
            'product_package_products.product_id',
            'product_package_products.quantity',
            DB::raw("CONCAT(product_groups.name, '- ', products.title, ' (', product_variants.variant_title , ')') AS title"),
            DB::raw("-(order_items.quantity) as used_quantity")
        )
        ->join('product_packages', 'product_courses.id', '=', 'product_packages.course_id')
        ->join('product_package_products', 'product_packages.id', '=', 'product_package_products.package_id')
        ->join('products', 'products.id', '=', 'product_package_products.product_id')
        ->join('product_groups', 'product_groups.id', '=', 'products.group_id')
        ->leftJoin('product_variants', 'product_variants.product_id', '=', 'products.id')
        ->leftJoin('order_items', 'order_items.variant_id', '=', 'product_variants.id')
        ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
        ->where('product_courses.id', $customer['course_id'])
        ->where('product_packages.package_for', $customer['gender_id'])
        ->where('order_items.type', 'sales')
        // ->whereIn('product_variants.id', $variantIds)
        ->where('order_items.order_id', $orderId)
        // ->where('orders.customer_id', $customer['id'])
        
        ->groupBy('products.id')
        ->get();
        return $courses;
    }

    

    public static function getCustomerProductsQtyOnlyInvoice($orderId, $customerId){
        
        $totalQuantities = DB::table('orders')
            ->join('order_items', 'order_items.order_id', '=', 'orders.id')
            ->whereIn('order_items.order_id', [$orderId])
            ->where('orders.customer_id', '=', $customerId)
            ->groupBy('order_items.product_id')
            ->select(DB::raw('SUM(order_items.quantity) as total_quantity'), 'order_items.product_id')
            ->get();

        return $totalQuantities;
    }

    public static function getCustomerProductsQty($products, $customerId, $saleType = "",$courseId = ""){
        
        $totalQuantities = DB::table('orders')
            ->join('order_items', 'order_items.order_id', '=', 'orders.id')
            // ->whereIn('order_items.product_id', $products)
            ->where('orders.customer_id', '=', $customerId);
            if(!empty($saleType)){
                $totalQuantities->where('orders.sale_type', '=', $saleType);
	    
	    }
	    if(!empty($courseId)){
                $totalQuantities->where('orders.course_id', '=', $courseId);
            }
            $totalQuantities = $totalQuantities->where('orders.status', '=', 'done')
            ->groupBy('order_items.product_id')
            ->select(DB::raw('SUM(order_items.quantity) as total_quantity'), 'order_items.product_id')
            ->get();

        return $totalQuantities;
    }
    public static function getCustomerProductsQtyAll($products, $customerId, $courseId){
        
        $totalQuantities = DB::table('orders')
            ->join('order_items', 'order_items.order_id', '=', 'orders.id')
            // ->whereIn('order_items.product_id', $products)
            ->where('orders.customer_id', '=', $customerId)
            ->where('orders.course_id', '=', $courseId)
            ->groupBy('order_items.product_id')
            ->select(DB::raw('-SUM(order_items.quantity) as total_quantity'), 'order_items.product_id')
            ->get();

        return $totalQuantities;
    }
}
