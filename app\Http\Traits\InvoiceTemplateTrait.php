<?php


namespace App\Http\Traits;


use App\Libraries\AllSettingFormat;
use App\Models\InvoiceTemplate;
use App\Models\Order;
use App\Models\OrderItems;
use App\Models\Payments;
use App\Models\Setting;
use App\Models\ShippingInformation;
use Illuminate\Support\Facades\Lang;
use Milon\Barcode\DNS1D;
use App\Models\Customer;
use App\Models\ProductPackage;
use App\Models\ProductCourse;
use DB;


trait InvoiceTemplateTrait
{
    public function replaceItemDetails($orderId, $template, $invoiceTemplateSize)
    {
        $checkReturn    = $this->checkIsReturnProduct($orderId); 
        if($checkReturn > 0){
            #jsut added order by ngative qty 
            $itemDetails = OrderItems::itemsForReturnInvoice($orderId);
        }else{
            $itemDetails = OrderItems::itemsForInvoice($orderId);
        }
        
         

        $orderType = Order::where("id", $orderId)->first('order_type')->order_type;
        $customer_id = Order::where("id", $orderId)->first('customer_id')->customer_id;
        $allSettingFormat = new AllSettingFormat;
        $row = "";

        if( $orderType == "sales"){
            foreach ($itemDetails as $item) {
                $item['price'] = "";
                $item['sub_total'] = '';
            }
        }
        $currentItem = '';
        $category = '';
        $total_qty = 0;
        $newTitle = '';
        foreach ($itemDetails as $index => $item) {

            if ($item['variant_title'] == 'default_variant') {
                $item['variant_title'] = '';
            } elseif ($item['type'] == 'shipment') {
                $item['variant_title'] = Lang::get('lang.shipping');
                $item['quantity'] = '';
                $item['discount'] = '';
                $item['price'] = '';

            } else {
                $item['variant_title'] = " ( " . $item['variant_title'] . " ) ";
            }

            if($invoiceTemplateSize === 'large'){
                $tempTdTag = '<td>';
            }elseif ($invoiceTemplateSize === 'thermal'){
                $tempTdTag = '<td>';
            }else{
                $tempTdTag = '<td style="border-bottom: 0" colspan="4">';
            }


            if ($item['type'] == 'shipment' || $item['type'] == 'discount') {
                $newRow = '';
            } else {

                //@jbn updated the table html
                $title = $item['note'] ?
                    $tempTdTag
                    . $this->getPaddedValue($index) . ". </td><td style='text-align:left;'>" . $item['title'] . $item['variant_title'] . '<br><small>Note: '
                    . $item['note'] . '</small></td>'
                    : $tempTdTag
                    . $this->getPaddedValue($index) . ". </td><td style='text-align:left;'>" . $item['title'] . $item['variant_title'] . '</td>';


                if ($invoiceTemplateSize === 'small') {
                    $newRow = $this->prepareTableForSmall($title, $item, $allSettingFormat);
                } elseif ($invoiceTemplateSize === 'thermal') {
                    $newRow = $this->prepareTableForThermal($title, $item, $allSettingFormat, $orderType);
                } else {
                    $newRow = $this->prepareTableForLarge($title, $item, $allSettingFormat, $orderType);
                }
            }
            // Displaying group details with total quantity
            if($orderType == "receiving"){
                if ($item['category_name'].' '.$item['title'] !== $newTitle && $newTitle != "") {
                    if ($currentItem !== '') {
                        $row = $row . "<tr><th colspan='4' style='text-align:center; font-weight:bold;'>{$newTitle}</th>
                        <th style='text-align:center;pr-0;'> {$total_qty}</th></tr>";
                        $total_qty = 0;
                       
                    }
                    $category = $item['category_name'];
                    $currentItem = $item['title'];
                   
                }
            }
            $total_qty = $total_qty + $item['ordered_quantity'];
            $row = $row . $newRow;
            $newTitle = $category.' '.$currentItem;
        }
        if($orderType == "receiving"){
            $row .= "<tr><th colspan='4' style='text-align:center; font-weight:bold;'>{$newTitle}</th>
                        <th style='text-align:center;pr-0;'> {$total_qty}</th></tr>";
        }

        // $showCombo      = $this->getInvoiceItemDetails($customer_id, $orderId);
             

        // if($showCombo && $invoiceTemplateSize == 'thermal' && $checkReturn <= 0){
        //     $searchFor = '{item_details}';
        //     $template = str_replace('{sales_header}', '', $template);
        //     return str_replace($searchFor, '', $template);
        // }
        // else if($showCombo && $invoiceTemplateSize == 'thermal' && $checkReturn > 0){
        //     $searchFor = '{item_details}';
        //     return str_replace($searchFor, $row, $template);
        // }else{
            $searchFor = '{item_details}';
            return str_replace($searchFor, $row, $template);
        // }
    }

    public function prepareTableForThermal($title, $item, $allSettingFormat, $orderType)
    {
        $tempTitleDiv = '<tr>' . $title;
        $sales_price_discount = Setting::getSettingValue('sales_price_discount')->setting_value;

        // return $tempTitleDiv . '<td>' .
        //     $allSettingFormat->getCurrency($allSettingFormat->thousandSep($item['price'])) .' x '.($allSettingFormat->thousandSep($item['quantity'])). '</td>
        //         <td class="dis">' . $allSettingFormat->thousandSep($item['discount']) . '%</td>
        //         <td>' . $allSettingFormat->getCurrency($allSettingFormat->thousandSep($item['sub_total'])) . '</td>
        //     </tr>';
        if($orderType === "sales"){
            // return $tempTitleDiv . '<td>' .' x '.($allSettingFormat->thousandSep($item['quantity'])). '</td>
            // </tr>';
            $order_item_id =isset($item['order_item_id'])  ? $item['order_item_id'] : null;
            if($order_item_id){
                $rec = DB::select(DB::raw("SELECT -(order_items.quantity) as quantity FROM order_items WHERE id = :order_item_id"), ['order_item_id' => $order_item_id]);                

                if(!empty($rec) && isset($rec[0]->quantity)){
                    $item['quantity'] = $rec[0]->quantity;
                }
            }
            return $tempTitleDiv . '<td>' .'  '.$item['quantity']. '</td>
            </tr>';
        }
        if($orderType === "receiving" || $sales_price_discount == "1"){
            return $tempTitleDiv . '<td>' .
            $allSettingFormat->getCurrency($allSettingFormat->thousandSep($item['price'])) .' x '.($allSettingFormat->thousandSep($item['quantity'])). '</td>
            <td class="dis">' . $allSettingFormat->thousandSep($item['discount']) . '%</td>
            <td>' . $allSettingFormat->getCurrency($allSettingFormat->thousandSep($item['sub_total'])) . '</td>
            </tr>';
        }
        else{
            return $tempTitleDiv . '<td>' .' x '.($allSettingFormat->thousandSep($item['quantity'])). '</td>
            </tr>';
        }
    }

    public function calTotalsGRN($orderId){
        $_sub_total_price = 0;
        $_total = 0;
        $_tax = 0; 

        $itemDetails = OrderItems::itemsForInvoice($orderId);
        foreach($itemDetails as $item){
           $itemTax = 0; 
            $is_grn = isset($_POST['is_grn']) ? $_POST['is_grn'] : '';
            $qty = $is_grn == 'Y' ? $item['quantity']: $item['ordered_quantity']; 
            $price  = $item['price'];
            $sub_total_price = $qty*$price;
        
            if($item['taxable'] == '1'){
                    if($item['tax_type'] == 'custom'){
                        $tax_id = $item['tax_id'];
                        $tax = DB::table('taxes')->find($tax_id);
                        $percentage = isset($tax->percentage) ? $tax->percentage  :0;
                    }else{
                        $tax = DB::table('taxes')->where('is_default', '1')->first();
                        $percentage = isset($tax->percentage) ? $tax->percentage  :0;
                    }
                    if($percentage > 0 && $sub_total_price > 0){
                        $itemTax = ($percentage / 100) * $sub_total_price;
                    } 
            }
            $total = (float)$sub_total_price + $itemTax;

            $_sub_total_price = $sub_total_price+ $_sub_total_price;
            $_total = $total+$_total;
            $_tax = $_tax+$itemTax;

        }

        $calc_data = ['sub_total'=> $_sub_total_price, 'total' => $_total, 'tax' => $_tax];
        return $calc_data;
    }

    public function prepareTableForLarge($title, $item, $allSettingFormat, $orderType )
    {
        $itemTax = 0;
        if($item['taxable'] == '1'){
            if($item['tax_type'] == 'custom'){
                $tax_id = $item['tax_id'];
                $tax = DB::table('taxes')->find($tax_id);
                $percentage = isset($tax->percentage) ? $tax->percentage  :0;
            }else{
                $tax = DB::table('taxes')->where('is_default', '1')->first();
                $percentage = isset($tax->percentage) ? $tax->percentage  :0;
            }
            if($percentage > 0 && $item['sub_total'] > 0){
                $itemTax = ($percentage / 100) * $item['sub_total'];
            } 
        }
        $pdt_unit = $item['short_name'];
        $total = (float) $item['sub_total'] + $itemTax;

        $tempTitleDiv = '<tr>' . $title;

        $is_grn = isset($_POST['is_grn']) ? $_POST['is_grn'] : '';

        $qty = $is_grn == 'Y' ? $item['quantity']: $item['ordered_quantity'];

        $sales_price_discount = Setting::getSettingValue('sales_price_discount')->setting_value;

        $price  = $item['item_price'];
        if($orderType == "receiving" || $sales_price_discount == "1"){
            $price  = $item['price'];
        }
       $sub_total_price =  $item['sub_total'];
       if($orderType == "receiving"){
           $sub_total_price = $qty*$price;
          
           if($item['taxable'] == '1'){
                if($item['tax_type'] == 'custom'){
                    $tax_id = $item['tax_id'];
                    $tax = DB::table('taxes')->find($tax_id);
                    $percentage = isset($tax->percentage) ? $tax->percentage  :0;
                }else{
                    $tax = DB::table('taxes')->where('is_default', '1')->first();
                    $percentage = isset($tax->percentage) ? $tax->percentage  :0;
                }
                if($percentage > 0 && $sub_total_price > 0){
                    $itemTax = ($percentage / 100) * $sub_total_price;
                } 
            }
            $total = (float)$sub_total_price + $itemTax;
        }
        $item_tax =  $allSettingFormat->thousandSep($itemTax);
        $sub_total = $allSettingFormat->getCurrency($allSettingFormat->thousandSep($sub_total_price));
        $price =  $allSettingFormat->getCurrency($allSettingFormat->thousandSep($price));
        $total = $allSettingFormat->getCurrency($allSettingFormat->thousandSep($total));

        if($orderType == "sales"){
            $item_tax = "";
            $price ="";
            $sub_total = "";
            $total ="";
        }
        
        if($orderType == "receiving"){
            $variant = str_replace(["(", ")"], "", $item['variant_title']);
            $data = explode(" - ",  $item['title']);
            if (count($data) == 2) {
                $title = $data[0];
                $gender = $data[1];
            } else {
                if (strpos($item['title'], "Male") !== false) {
                    $title = str_replace(" Male", "", $item['title']);
                    $gender = "Male";
                } elseif (strpos($item['title'], "Female") !== false) {
                    $title = str_replace(" Female", "", $item['title']);
                    $gender = "Female";
                } else {
                    $title = $item['title'];
                    $gender = "";
                }
            }
            return
                '<td class="text-center pr-0">' . $item['category_name'] . '</td>
                <td class="text-center pr-0">' . $title . '</td>
                <td class="text-center pr-0">'.  $gender  .'</td>
                <td class="text-center">' . $variant. '</td>
                <td style="text-align:center;pr-0;">' . $item['ordered_quantity'] . '</td>
                </tr>';
            }else{
            return $tempTitleDiv .
                '<td class="text-right pr-0">' . $pdt_unit . '</td>
                <td class="text-right">' . intval($allSettingFormat->thousandSep($qty)) . '</td>
                <td class="text-right ">' . $price . '</td>
                <td class="text-right pr-0">' . $sub_total . '</td>
                </tr>';
            }
    }

    public function prepareTableForSmall($title, $item, $allSettingFormat)
    {
        $tempTitleDiv = '<tr>' . $title . '</tr><tr>';

        return $tempTitleDiv . '<td class="w-25">' . $allSettingFormat->getCurrency($allSettingFormat->thousandSep($item['price'])) . '</td>
                <td class="text-right pr-0">' . intval($allSettingFormat->thousandSep($item['quantity'])) . '</td>
                <td class="text-right pr-0">' . $allSettingFormat->thousandSep($item['discount']) . '%</td>
                <td class="text-right pr-0">' . $allSettingFormat->getCurrency($allSettingFormat->thousandSep($item['sub_total'])) . '</td>
            </tr>';

    }

    public function replacePaymentDetails($orderId, $template, $invoiceTemplateSize)
    {
        $allSettingFormat = new AllSettingFormat;
        $paymentDetails = Payments::getPaymentDetails($orderId);

        $row = "";

        $colspan = $invoiceTemplateSize == 'large' ? 4 : 3;

        $styleOrClass = $invoiceTemplateSize === 'thermal' ?  'style="text-align:left;"' : 'class="text-left"';

        foreach ($paymentDetails as $item) {

            $newRow = '
            <tr class="t-footer">
                <td '.$styleOrClass.'>' . $item['name'] . '</td>
                <td class="font-weight-bold" colspan="' . $colspan . '">' . $allSettingFormat->getCurrency($allSettingFormat->thousandSep($item['paid'])) . '</td>
            </tr>';
            $row = $row . $newRow;
        }

        $searchFor = '<tr><td>{payment_details}</td></tr>';

        return str_replace($searchFor, $row, $template);

    }

    public function getTemplate($cashRegisterId, $orderType)
    {
        if ($cashRegisterId == null) {
            $data = InvoiceTemplate::getInvoiceTemplateForNoCashReg($orderType);
        } else {
            $data = InvoiceTemplate::getInvoiceTemplateToPrint($cashRegisterId, $orderType);
        }

        return $data;
    }

    public function singleReportInvoiceTemplate($cashRegisterId, $orderType, $invoiceSize = 'large')
    {
        if ($cashRegisterId == null) {
            $data = InvoiceTemplate::getReportTemplateWithoutCashRegister($orderType, $invoiceSize);
        } else {

            $data = InvoiceTemplate::getReportTemplateWithCashRegister($cashRegisterId, $orderType);
        }

        return $data;
    }

    public function getInvoiceLogo($from, $template, $orderType)
    {
        $configKey = $orderType === 'sales' ? 'invoiceLogo' : 'purchase_invoiceLogo';
        $src = url('/') . '/uploads/logo/' . config($configKey);
        $template = str_replace('{logo_source}', $src, $template);

        return $template;
    }

    public function replaceSpecificInfo($template, $orderId, $orderType, $cashRegisterId, $salesOrReceivingType, $transferBranchName)
    {
        $allSettingFormat = new AllSettingFormat;
        $is_grn = isset($_POST['is_grn']) ? $_POST['is_grn'] : '';

        $_orderId = $orderId;

        if($is_grn == 'Y'){
            $invoiceId = Order::where('id', $orderId)->value('invoice_id');
            $order = Order::where('invoice_id', $invoiceId)
                        ->orderBy('id', 'asc')
                        ->first(['id']);
            $orderId = $order->id;
        }
        // \DB::enableQueryLog();
        $orderDetails = Order::getOrderDetailsForInvoice($orderId, $orderType, $cashRegisterId);
        // dd(\DB::getQueryLog());

        $discountAmount = OrderItems::getDiscountAmount($orderId);
        $orderType = Order::where("id", $orderId)->first('order_type')->order_type;
        $sales_price_discount = Setting::getSettingValue('sales_price_discount')->setting_value;
        
        $orderCustomerState = \Config::get('client_location_state');
        if ($orderDetails->customer_name == null) {
            $orderDetails->customer_name = Lang::get('lang.walk_in_customer');
        }else{
            $orderCustomerState = $orderDetails->state;
        }
        $clientState    = \Config::get('client_location_state'); 
        
        $gstType        = $clientState == $orderCustomerState ? 'SGST' : (is_null($orderCustomerState) ? 'SGST' : 'IGST');
		
        $gstin          = $orderDetails->gstin_number;

        $replace = $this->replaceInvoiceRandomInfo($orderDetails, $allSettingFormat, $orderType, $_orderId);
        if($orderType == "receiving" || $sales_price_discount == "1"){
            $replace['{discount}'] = $this->replaceDiscount($discountAmount, $allSettingFormat);
        }else{
            $replace['{discount}'] = '';
        }
        $template = $this->replaceShipmentDetails($template, $orderId, $orderType);

        $template = $this->replaceReturnDetails($template, $orderId, $orderType,$allSettingFormat);
        $replace['{table_name}'] = '';
        if ($orderDetails->table_id != null) {
            $replace['{table_name}'] = $orderDetails->table_name;
        }

        if(!is_null($orderDetails->customer_id)){
           $customer = DB::table('customers')->where('id', $orderDetails->customer_id)->first();
           $customer_name = $customer->first_name ." ". $customer->last_name ? $customer->last_name : '';
           $replace['{customer_name}'] = $customer_name ? $customer_name : '';
           $replace['{roll_no}'] = $customer->roll_no ? $customer->roll_no : '';
   
           $course = ProductCourse::where('id', $customer->course_id)->first();

           if($course){
            $replace['{course_name}'] = $course->course_name ? $course->course_name : '';
            $replace['{phone}'] = $customer->phone_number ? $customer->phone_number : '';
            }else{
             $replace['{course_name}'] = '';
             $replace['{phone}'] = '';
            }


         
        }

        $orderDetails->tin_number ? $replace['{tin}'] = $orderDetails->tin_number : $replace['{tin}'] = '';

        if ($orderDetails->invoice_id) {
            $barcode = '<img src="data:image/png;base64,' . (new DNS1D)->getBarcodePNG($orderDetails->invoice_id, "C39") . '" alt="barcode" />';
            $template = str_replace('{barcode}', $barcode, $template);
        }


        if ($orderType == 'sales') {

            $orderDetails->sales_note ? $replace['{note}'] = $orderDetails->sales_note : $replace['{note}'] = '';
            $orderDetails->phone_number ? $replace['{phone_number}'] = $orderDetails->phone_number : $replace['{phone_number}'] = '';
            $orderDetails->address ? $replace['{address}'] = $orderDetails->address : $replace['{address}'] = '';
            $salesOrReceivingType == 'customer' ? $replace['{customer_name}'] = $orderDetails->customer_name : $replace['{customer_name}'] = $transferBranchName;

            $taxPay = $orderDetails->total_tax;
            $gstTax = 0;
            if($taxPay > 0){
                $gstTax = round($taxPay/2, 2);                
            }

            $orderDetails->gstin_number ? $replace['{gstin}'] = $orderDetails->gstin_number : $replace['{gstin}'] = '';
            $replace['{cgst_label}']    = 'CGST';
            $replace['{s_gst_label}']   = $gstType;
            // $orderDetails->total_tax ? $replace['{cgst_tax}'] = $gstTax : $replace['{cgst_tax}'] = '';
            // $orderDetails->total_tax ? $replace['{s_gst_tax}'] = $gstTax : $replace['{s_gst_tax}'] = ''; 
            
            if($orderType == "receiving" || $sales_price_discount == "1"){
                $orderDetails->total_tax ? $replace['{cgst_tax}'] = $gstTax : $replace['{cgst_tax}'] = '';
                $orderDetails->total_tax ? $replace['{s_gst_tax}'] = $gstTax : $replace['{s_gst_tax}'] = ''; 
            }
            else{
                $replace['Subtotal'] ='';
                $replace['Shipment'] ='';
                $replace['CGST'] ='';
                $replace['{cgst_label}'] = '';
                $replace['{s_gst_label}'] = '';
                $replace['SGST'] ='';
                $replace['Total'] ='';
                $replace['Paid in Advance'] ='';
                $replace['Discount'] = '';
                $replace['Price'] = '';
                $replace['Disc%'] = '';
                $orderDetails->total_tax ? $replace['{cgst_tax}'] = "" : $replace['{cgst_tax}'] = '';
                $orderDetails->total_tax ? $replace['{s_gst_tax}'] = "" : $replace['{s_gst_tax}'] = ''; 
            }

            $checkReturn = $this->checkIsReturnProduct($orderId);

            #replace title
            $title          = 'Tax Invoice';        
            if($checkReturn > 0 ){
                $title          = 'Return / Exchange Bill'; 
                $replace['{invoice_title}'] = $title;           
            }
            $replace['{invoice_title}'] = $title;    


            #replace package details
            $package_details = '';
            $showCombo = $this->getInvoiceItemDetails($orderDetails->customer_id, $orderId);
            // dd($orderDetails);

            if(!is_null($orderDetails->customer_id) && $showCombo === true && $orderDetails->sale_type == 'combo'){
                $cid = $orderDetails->customer_id;
                $customer   =   Customer::customerDetails($cid);
                $is_gender = isset($customer->gender_id) ? $customer->gender_id : '';
                // \DB::enableQueryLog();
                $getPackage =   ProductPackage::getPackageDetails2($customer, $orderId);
                // dd(\DB::getQueryLog());

                $courseInfo =   ProductCourse::getOne($customer['course_id']);

                $package_name = isset($courseInfo->course_name) ? $courseInfo->course_name : '';

                $productIds = [];
                if(!empty($getPackage)){
                    foreach($getPackage as $key => $rec){
                        $productIds[] = $rec->product_id;
                    }
                } 

                $getPackage2 =   ProductPackage::getPackageDetails1($customer, $productIds);  
                $checkReturn = $this->checkIsReturnProduct($orderId);

                    //its retun invoice 
                    $package_details = '';       
                    #create array 
                    #show combo overal slae info                  
                    $getPackageAll      =   ProductPackage::getPackageDetailsAll($customer);             
                    $getPackageNotSale  =   ProductPackage::getPackageDetailsNotSale($customer);
                    $customerQuantitiesAll = ProductPackage::getCustomerProductsQtyAll($productIds, $cid, $customer['course_id']);
                    $return_package_details = '';
                     
                    if(!empty($getPackageAll) || !empty($getPackageNotSale) ){
                        $return_package_details .= "<div class='page-break'>".'<div class="row">                        
                        <div class="col-md-12 combo_pack_block">  
                            <div style="font-size:24px;color:#000;font-weight:bold;"  >Student Combo Summary</div>
                            <div style="font-size:20px;color:#000;" class="bundle-pack-title">'.$package_name.'</div>                          
                            <table style="font-size:20px;">
                                <tbody>
                                    <tr>
                                        <th>Product</th>
                                        <th width="10%">Qty.</th> 
                                        <th width="10%">Issued</th>
                                        <th width="10%">Balance</th>
                                    </tr>';

                        if(!empty($getPackageAll)){
                            foreach($getPackageAll as $key => $rec){        
                                $issued    = 0;
                                $issuing   = 0;
                                $balance   = $rec->quantity;  

        
                                if(!empty($customerQuantitiesAll)){
                                    foreach($customerQuantitiesAll as $k => $rec1){
                                        if($rec1->product_id == $rec->product_id){
                                            $issued = abs($rec1->total_quantity);
                                            $balance = $rec->quantity - abs($rec1->total_quantity);      
                                        }
                                    }
                                } 

                                #new only show current invoice quntity
                                $issued = abs($rec->used_quantity);
                                $balance =  $rec->quantity - $issued;   
        
                                $return_package_details .='<tr>
                                        <td>'.$rec->title.' </td> 
                                        <td>'.$rec->quantity.'</td>
                                        <td>'.$issued.'</td>
                                        <td>'.$balance.'</td>
                                    </tr>';
                            }
                        } 

                        if(!empty($getPackageNotSale)){
                            foreach($getPackageNotSale as $key => $rec){        
                                $issued    = 0;
                                $issuing   = 0;
        
                                $return_package_details .='<tr>
                                        <td>'.$rec->title.' </td> 
                                        <td>'.$rec->quantity.'</td>
                                        <td>'.$issued.'</td>
                                        <td>'.$rec->quantity.'</td>
                                    </tr>';
                            }
                        }
                        
                        $return_package_details .=  '</tbody>
                                            </table>
                                        </div>
                                    </div>';
                        if($is_gender == 'F'){
                            $return_package_details .='<br /><div>
                                <div style="margin-bottom:15px;"><strong>Note:</strong></div>
                                    <div style="margin-bottom:15px;">Female students may choose one of the following clothing combinations:</div>
                                    <div><b>1. T-Shirt with Jeans</b></div>
                                    <div><b>2. Kurthis with Leggings</b></div>
                                    <br/>
                                    <div>Please note that selecting one combination will make the other option ineligible.</div>
                                </div>';
                        }  
                        $return_package_details .='</div>'; 
                    } 
                    $replace['{return_package_details}'] = $return_package_details;  

                  
            } else{
                if($checkReturn > 0 && $showCombo === true){
                    $replace['{return_package_details}'] = '<div style="text-align:center;">No Combo Available for this Student</div>';      
                }else{
                    $replace['{return_package_details}'] = '';      
                }
                
            }
            $replace['{package_details}'] = $package_details;
        } else { 
            
            
            
            if ($salesOrReceivingType == 'supplier') {
                if ($orderDetails->supplier_name == null) {
                    $orderDetails->supplier_name = Lang::get('lang.walk_in_supplier');
                }
                $replace['{supplier_name}'] = $orderDetails->supplier_name;
                $orderDetails->address ? $replace['{address}'] = $orderDetails->address : $replace['{address}'] = '';
            } else {
                // $replace['{supplier_name}'] = $transferBranchName;
                $replace['{supplier_name}'] = $transferBranchName ? $transferBranchName : $orderDetails->supplier_name;
                $orderDetails->address ? $replace['{address}'] = $orderDetails->address : $replace['{address}'] = '';
            }
        }

        return strtr($template, $replace);
    }

    function getInvoiceItemDetails($customer_id, $orderId){
        $showCombo=false;

        if(!is_null($customer_id)){
             #start
            $customerSubquery = DB::table('customers')->select('course_id', 'gender_id') ->where('id', $customer_id)->first();
            $courseId = $customerSubquery->course_id;
            $genderId = $customerSubquery->gender_id;

            // Step 2: Find the package ID from product_packages
            $packageIdSubquery = DB::table('product_packages')
                ->select('id')
                ->where('course_id', $courseId)
                ->where('package_for', $genderId)
                ->first();

            $packageId = isset($packageIdSubquery->id) ? $packageIdSubquery->id  : null;
            if($packageId == null){
                $packageId = 0;
            }

            // Step 3: Get the product IDs from product_package_products using GROUP_CONCAT
            $productIds = DB::table('product_package_products')
                ->where('package_id', $packageId)
                ->select(DB::raw('GROUP_CONCAT(product_id) as product_ids'))
                ->value('product_ids');

                if(!empty($productIds)){
                    $showCombo = true;
                }

                // if($showCombo){
                //     $cartProductIds = DB::table('order_items')
                //     ->select(DB::raw('GROUP_CONCAT(product_id) as product_ids'))
                //     ->where('order_id', $orderId)
                //     ->value('product_ids');

                //     $showCombo = $this->hasMatchingProductId($cartProductIds, $productIds );
                // }
            #end
        }
        return $showCombo;
    }

    function hasMatchingProductId($cartProductIds, $productIds) {

        $cartProductIds = explode(',',$cartProductIds);
        $productIds     = explode(',',$productIds);

        // Find the intersection of both arrays
        $intersection = array_intersect($cartProductIds, $productIds);
    
        // Return true if the intersection is not empty, otherwise return false
        return !empty($intersection);
    }

    public function replaceReturnDetails($template, $orderId, $orderType, $allSettingFormat){
        // 
        $returns = OrderItems::where('order_id', '=', $orderId)
        ->where('sub_total', '<', 0)
        ->sum('sub_total');

        $new_sales = OrderItems::where('order_id', '=', $orderId)
        ->where('sub_total', '>', 0)
        ->sum('sub_total'); 

        $returns    = $allSettingFormat->getCurrency($allSettingFormat->thousandSep(abs($returns)));
        $new_sales  = $allSettingFormat->getCurrency($allSettingFormat->thousandSep($new_sales));


        if ($orderType == 'sales' && $returns != "₹0.00" ) {
            $refund_payments = '<tr>
                <td>Return sales amount</td>
                <td> '.$returns.'</td>
            </tr>
            <tr style="border-bottom: 1px dashed #ccc;" >
                <td>New Sales </td>
                <td>'.$new_sales.'</td>
            </tr>';
           
            $template = str_replace('{refund_payments}', $refund_payments, $template);
        }else{
            $template = str_replace('{refund_payments}', '', $template);
        }

        return $template;

    }

    public function replaceShipmentDetails($template, $orderId, $orderType)
    {
        $allSettingFormat = new AllSettingFormat;
        $shipmentInformation = ShippingInformation::orderShipment($orderId);

        $shippingAddress = '';
        if ($shipmentInformation != null) {
            $shippingAddress = '<p class="mb-1">
                        <span class="font-weight-bold">Shipping Address: </span>' . $shipmentInformation->shipping_address . '
                    </p>';
        }

        if ($orderType == 'sales') {

            $price = $shipmentInformation ? $allSettingFormat->getCurrency($allSettingFormat->thousandSep($shipmentInformation->price)) : '';
            $template = str_replace('{shipment_amount}', $price, $template);
            $template = str_replace('{shipment_address}', $shippingAddress, $template);
        }

        return $template;
    }

    public function replaceInvoiceRandomInfo($orderDetails, $allSettingFormat, $orderType, $_orderId=null)
    {
        if($orderDetails->total_tax <= 0){
            $orderDetails->total_tax = $orderDetails->total - $orderDetails->sub_total;
        }
        $sales_price_discount = Setting::getSettingValue('sales_price_discount')->setting_value;
        if($orderType == "receiving" || $sales_price_discount == "1"){

            $sub_total = $allSettingFormat->getCurrency($allSettingFormat->thousandSep($orderDetails->sub_total));
            $total = $allSettingFormat->getCurrency($allSettingFormat->thousandSep($orderDetails->total));
            $tax = $allSettingFormat->getCurrency($allSettingFormat->thousandSep($orderDetails->total_tax));

            $is_grn = isset($_POST['is_grn']) ? $_POST['is_grn'] : '';
            if($orderType == "receiving"){

                $newOrderIdRe = $is_grn != 'Y'? $orderDetails->id : $_orderId;
                $data = $this->calTotalsGRN($newOrderIdRe);
                $sub_total = $allSettingFormat->getCurrency($allSettingFormat->thousandSep($data['sub_total']));
                $total = $allSettingFormat->getCurrency($allSettingFormat->thousandSep($data['total']));
                $tax  = $allSettingFormat->getCurrency($allSettingFormat->thousandSep($data['tax']));
            }
            return [
                '{app_name}' => config('app_name'),
                '{invoice_id}' => $orderDetails->invoice_id,
                '{employee_name}' => $orderDetails->employee_name,
                '{date}' => $allSettingFormat->getDate($orderDetails->date),
                '{time}' => $allSettingFormat->timeFormat($orderDetails->created_at),
                '{sub_total}' => $sub_total,
                '{tax}' => $tax,
                '{total}' => $total,
                '{exchange}' => $allSettingFormat->getCurrency($allSettingFormat->thousandSep($orderDetails->exchange)),
            ];
        }
        else{
            return [
                '{app_name}' => config('app_name'),
                '{invoice_id}' => $orderDetails->invoice_id,
                '{employee_name}' => $orderDetails->employee_name,
                '{date}' => $allSettingFormat->getDate($orderDetails->date),
                '{time}' => $allSettingFormat->timeFormat($orderDetails->created_at),
                '{sub_total}' => '',
                '{tax}' => '',
                '{total}' => '',
                '{exchange}' => $allSettingFormat->getCurrency($allSettingFormat->thousandSep($orderDetails->exchange)),
            ];
        }
        
    }

    public function replaceDiscount($discountAmount, $allSettingFormat)
    {
        if ($discountAmount != null) {
            return $allSettingFormat->getCurrency($allSettingFormat->thousandSep($discountAmount->overAllDiscount));
        } else {
            return $allSettingFormat->getCurrency($allSettingFormat->thousandSep(0.00));
        }
    }

    public function getPaddedValue($value)
    {
        return str_pad($value + 1, 2, "0", STR_PAD_LEFT);
    }

    public function replaceThermalPaymentDetails($orderId, $template, $invoiceTemplateSize)
    {
        $allSettingFormat = new AllSettingFormat;
        $paymentDetails = Payments::getPaymentDetails($orderId);
        $orderType = Order::where("id", $orderId)->first('order_type')->order_type;
        $sales_price_discount = Setting::getSettingValue('sales_price_discount')->setting_value;

        
        $row = "";

        if($orderType == "receiving" || $sales_price_discount == "1"){
            foreach ($paymentDetails as $item) {
                $newRow = '
                <tr>
                    <td>' . $item['name'] . '</td>
                    <td>' . $allSettingFormat->getCurrency($allSettingFormat->thousandSep($item['paid'])) . '</td>
                </tr>';
                $row = $row . $newRow;
            }
        }else{
            foreach ($paymentDetails as $item) {
                $newRow = '
                <tr>
                    <td>' . $item['name'] . '</td>
                    <td>' . ' ' . '</td>
                </tr>';
                $row = $row . $newRow;
            }
        }
        $searchFor = '<tr>{payment_details}</tr>';

        
        return str_replace($searchFor, $row, $template);

    }

    public function checkIsReturnProduct($orderId){
        return  DB::table('order_items')
                                ->where('order_id', $orderId)
                                ->where('quantity', '>', 0)
                                ->count();
    }
}