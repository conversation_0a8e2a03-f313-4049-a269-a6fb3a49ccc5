<?php

namespace App\Http\Controllers\Cron;

use App\Libraries\AllSettingFormat;
use App\Models\OrderItems;
use App\Models\Setting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use PDF;
use File;
use App\Libraries\Email;
use Illuminate\Support\Facades\Storage;
 

class TestController extends Controller
{
    public function TestCode()
    {
        $filePath = public_path('uploads/updated1.csv');
        // Check if the file exists
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return response()->json(['error' => 'File not found or not readable'], 404);
        }

        $data = [];
        
        // Open the file for reading
        if (($handle = fopen($filePath, 'r')) !== false) {
            // Loop through each row of the file
            while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                $data[] = $row;  // Add each row to the data array
            }
            fclose($handle); // Close the file after reading
        }

        // Process $data or return it as needed

        if(!empty($data)){
            $init = 0;
            foreach($data as $key => $rec){
                
                $message = '';
                $roll_no = $phone = $email = '';
                $roll_no = $rec[0];
                $phone = $rec[1];
                // echo "<pre>"; print_r($rec); echo "</pre>";
                // \DB::enableQueryLog();
                $roll_no_old_obj = \DB::table('customers')->where("roll_no", $roll_no)->get();
                // dd(DB::getQueryLog());
                // dd($roll_no_old_obj);

                if(!empty($roll_no_old_obj)){
                    foreach($roll_no_old_obj as $key1 => $rec1){
                        $id = $rec1->id;
                        $roll_no_old    = isset($rec1->roll_no) ? $rec1->roll_no : ''; 
                        $aadhar_old     = isset($rec1->aadhr_card_no) ? $rec1->aadhr_card_no : ''; 
                        $roll_no_old_len = strlen($roll_no_old);

                        if($aadhar_old == "" && $roll_no_old_len == 12){
                            $init++;
                           \DB::table('customers')->where('id', $id)->update([ 'aadhr_card_no' => $roll_no_old ]);                                 
                        }

                       \DB::table('customers')->where('id', $id)->update([ 'roll_no' => $phone ]);

                        $message .= "<pre>   roll no : $roll_no_old ---- to ---- $phone updated </pre>\n ";
                        print $message;

                    }
                } 
            }
        }
        // return response()->json($data);

        
    }

    public function UpdateCode()
    {
        $filePath = public_path('uploads/cust.csv');
        // Check if the file exists
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return response()->json(['error' => 'File not found or not readable'], 404);
        }

        $data = [];
        
        // Open the file for reading
        if (($handle = fopen($filePath, 'r')) !== false) {
            // Loop through each row of the file
            while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                $data[] = $row;  // Add each row to the data array
            }
            fclose($handle); // Close the file after reading
        }

        if(!empty($data)){
            $init = [];
            foreach($data as $key => $rec){
                
                $message    = '';
                $ids        = isset($rec[7]) ? $rec[7] : '';
                $roll_no    = isset($rec[5]) ? trim($rec[5]) : '';
                if($ids != ""){
                    $idAry = explode("/", $ids);

                    if(!empty($idAry)){
                        foreach($idAry as $key => $rec){
                            $roll_no_old_obj = \DB::table('orders')->where("customer_id", $rec)->first();

                            if(empty($roll_no_old_obj)){
                                
                                echo "<prE>"; print_r($rec); echo "--------=>--------"; echo $ids; echo "</pre>";
                                $init[]= $rec;
                                // \DB::enableQueryLog();
                                 \DB::table('customers')->where("id", $rec)->delete();
                                // $lastQuery = \DB::getQueryLog();
                                
                                // echo "<prE>"; print_r(end($lastQuery)); echo "</prE>";   

                            }else{
                                $roll_no_obj = \DB::table('customers')->where("id", $rec)->where("roll_no", $roll_no)->first();
                                if(empty($roll_no_obj) && $roll_no != ""){
                                    \DB::table('customers')
                                        ->where('id', $rec)
                                        ->update(['roll_no' => $roll_no]);
                                }
                            }
                        }
                    }
                } 
            }
            dd($init);
        }
    }


    public function RemoveStudents()
    {
        $filePath = public_path('uploads/student.csv');
        // Check if the file exists
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return response()->json(['error' => 'File not found or not readable'], 404);
        }

        $data = [];
        
        // Open the file for reading
        if (($handle = fopen($filePath, 'r')) !== false) {
            // Loop through each row of the file
            while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                $data[] = $row;  // Add each row to the data array
            }
            fclose($handle); // Close the file after reading
        }

        if(!empty($data)){
            $init = [];
            unset($data[0]);
            $init = 0;
            foreach($data as $key => $rec){
                // echo "<pre>"; echo $key; echo "</pre>";
                $roll_no    = isset($rec[0]) ? $rec[0] : '';
                $phone      = isset($rec[1]) ? $rec[1] : '';
                $email      = isset($rec[2]) ? $rec[2] : '';

                $cust_obj = \DB::table('customers')->whereRaw('LOWER(roll_no) = ?', [strtolower($roll_no)])->first();
                if(empty($cust_obj)){
                    // echo "<pre>"; echo $roll_no; echo "</pre>";
                }else{
                    $cust_id = null;
                    $cust_id = $cust_obj->id;

                    $order_obj = \DB::table('orders')->where("customer_id", $cust_id)->first();
                    if(!empty($order_obj)){
                        $init++;
                        echo "<pre> order exists"; echo $cust_id; echo "---------------- $init </pre>";
                    }else{
                        \DB::table('customers')->where("id", $cust_id)->delete();
                    }
                    
                }
            }
           
        }

        echo "done";
    }
}
