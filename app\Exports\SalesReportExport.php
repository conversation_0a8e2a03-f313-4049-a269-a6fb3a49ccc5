<?php

namespace App\Exports;

use App\Models\OrderItems;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class SalesReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {   
        $branchIds = explode(',', Auth::user()->branch_id);
        return OrderItems::query()->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->leftJoin('products', 'products.id', '=', 'order_items.product_id')
            ->join('users', 'users.id', '=', 'orders.created_by')
            ->leftJoin('users as users_tbl' , 'users_tbl.id', '=', 'orders.sales_user_id')
            ->leftJoin('taxes', 'taxes.id', '=', 'order_items.tax_id')
            ->leftJoin('customers', 'customers.id', '=', 'orders.customer_id')
            ->leftJoin('branches', 'branches.id', 'orders.transfer_branch_id')            
            ->leftJoin('payments', 'payments.order_id', 'orders.id')            
            ->leftJoin('payment_types', 'payment_types.id', 'payments.payment_method')  
            ->select(
                'products.title',
                'orders.id',
                'branches.name as transfer_branch_name',
                'orders.date',
                'orders.type',
                'orders.sub_total',
                'orders.total_tax as tax',
                'orders.total',
                'orders.sales_user_id',
                'orders.sales_note',
                // DB::raw("ROUND(`orders`.`total`)  AS total"),
                'orders.invoice_id',
                'orders.due_amount',
                'payment_types.name',
                'customers.roll_no',
                DB::raw("CASE 
                        WHEN orders.status = 'done' THEN 'Active'
                        WHEN orders.status = 'delete' THEN 'Inactive'
                        WHEN orders.status = 'cancelled' THEN 'Cancelled'
                        ELSE orders.status
                    END as status"),
                DB::raw("CONCAT(users.first_name,' ',users.last_name)  AS created_by"),
                DB::raw("CONCAT(users_tbl.first_name,' ',users_tbl.last_name)  AS sales_user_name"),
                DB::raw("users.id as user_id"),
                DB::raw("CONCAT(customers.first_name,' ',customers.last_name)  AS customer"),
                DB::raw("customers.id as customer_id"),
                DB::raw("(sum(((abs(order_items.quantity)*order_items.price)* order_items.discount)/100)) AS discount"),
                DB::raw(' TRUNCATE((( SUM(CASE WHEN order_items.type = "discount" THEN 0 ELSE TRUNCATE(order_items.quantity,2) END) * -1 )),2) as item_purchased')
            )
            ->where('orders.order_type', '!=', 'receiving')
            // ->where('orders.status', '=', 'done')
            ->whereIn('orders.branch_id', $branchIds)
            ->where('orders.order_type', '=', 'sales')
            ->groupBy('order_items.order_id');
    }

    public function map($reportRow): array
    {       
        return 
            [
                $reportRow->invoice_id,
                $reportRow->date,
                $reportRow->status,
                $reportRow->created_by,
                $reportRow->roll_no,
                $reportRow->customer ??  Lang::get('lang.walk_in_customer'),
                $reportRow->item_purchased,
                number_format((float)$reportRow->tax, 2, '.', ''),
                number_format((float)$reportRow->discount, 2, '.', ''),
                number_format((float)$reportRow->total, 2, '.', ''),
                number_format((float)$reportRow->due_amount, 2, '.', ''),
                $reportRow->sales_note,
            ];                    
    }

    public function headings(): array
    {
        return [
            Lang::get('lang.invoice_id'),
            Lang::get('lang.date'),
            Lang::get('lang.status'),            
            Lang::get('lang.sold_by'),                       
            Lang::get('lang.roll_number'),
            Lang::get('lang.sold_to'), 
            Lang::get('lang.item_purchased'),
            Lang::get('lang.tax'),
            Lang::get('lang.discount'),
            Lang::get('lang.total'),
            Lang::get('lang.due'),
            Lang::get('lang.remarks'),
            
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->getStyle('A1:H1')->applyFromArray([
                        'font' =>[
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' =>[
                                'color' => ['argb' =>'FFFF0000']
                            ],
                        ]
                    ],
                );
                
                
                $query_result = $this->query()->get();
                $rows_count = count($query_result);
                    
                $item_purchase  = $query_result->sum('item_purchased');  
                $tax            = $query_result->sum('tax');
                $discount       = $query_result->sum('discount');
                $total          = $query_result->sum('total');
                $due_amount     = $query_result->sum('due_amount');
                                
                $rows_count = $rows_count + 2;

                $event->sheet->setCellValue('A'.$rows_count, 'Total'); 
                $event->sheet->setCellValue('F'.$rows_count, number_format($item_purchase,2));
                $event->sheet->setCellValue('G'.$rows_count, number_format($tax,2));
                $event->sheet->setCellValue('H'.$rows_count, number_format($discount,2));
                $event->sheet->setCellValue('I'.$rows_count, number_format($total,2));
                $event->sheet->setCellValue('J'.$rows_count, number_format($due_amount,2));
                
            },
        ];
    }
    
}
