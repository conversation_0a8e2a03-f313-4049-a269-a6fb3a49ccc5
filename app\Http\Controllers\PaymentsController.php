<?php

namespace App\Http\Controllers;

use App\Imports\CustomerPaymentsImport;
use App\Imports\CustomersImport;
use App\Imports\ProductVariantsImport;
use App\Models\Customer;
use Illuminate\Http\Request;
use App\Models\CustomerPayment;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class PaymentsController extends Controller
{
    /**
     * Insert payment data to the customer_payments table.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function singleStore(Request $request)
    {
        // Define validation rules
        $validator = Validator::make($request->all(), [
            'roll_no' => 'required',
            'fees' => 'required|numeric',
            'balance' => 'required|numeric',
            'paid' => 'required|numeric'
        ]);

        // Validate the request data
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create a new CustomerPayment
            $payment = new CustomerPayment();
            $payment->roll_no = $request->input('roll_no');
            $payment->fees = $request->input('fees');
            $payment->balance = $request->input('balance');
            $payment->paid = $request->input('paid');
            $payment->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Payment data inserted successfully',
                'data' => $payment
            ], 201);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while inserting payment data',
                'error' => $e->getMessage()
            ], 500);
        }
    }


    public function store(Request $request)
{
    // Define validation rules for the uploaded file
    $validator = Validator::make($request->all(), [
        'file' => 'required|mimes:xlsx'
    ]);

    // Validate the request data
    if ($validator->fails()) {
        return response()->json([
            'success' => false,
            'message' => 'Validation errors',
            'errors' => $validator->errors()
        ], 422);
    }

    try {
        // Import the data from the uploaded Excel file
        Excel::import(new CustomerPaymentsImport, $request->file('file'));

        // Return success response
        return response()->json([
            'success' => true,
            'message' => 'Payment data imported successfully'
        ], 201);
    } catch (\Exception $e) {
        // Return error response
        return response()->json([
            'success' => false,
            'message' => 'An error occurred while importing payment data',
            'error' => $e->getMessage()
        ], 500);
    }
}


    public function getLatestByRollNo(Request $request)
    {
        // Validate the roll_no parameter
        $request->validate([
            'id' => 'required'
        ]);
       
        try {
             // Find the  roll_no by using id
            // $customer_roll_no = Customer::where("id", $request->input('id'))->pluck('roll_no');
            // Find the latest record by roll_no
            $latestPayment = CustomerPayment::where('customer_id', $request->input('id'))
            ->latest()
            ->first();
            
            if ($latestPayment) {
                if(isset($request->responseStatus) && $request->responseStatus == true) {
                    return ['customerAmount' => $latestPayment];
                }
                return response()->json([
                    'success' => true,
                    'data' => $latestPayment
                ], 200);
            } else {
                if(isset($request->responseStatus) && $request->responseStatus == true) {
                    return ['customerAmount' => $latestPayment];
                }
                return response()->json([
                    'success' => false,
                    'message' => 'No records found for the given Roll No'
                ], 404);
            }
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching the latest payments record',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function import(Request $request)
    {
        // Validate the request
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv',
        ]);

        // Import the file
        $import = new CustomersImport();
        Excel::import($import, $request->file('file'));

        $errors = $import->getErrors();
        $insertedCount = $import->getInsertedCount();

        if (empty($errors)) {
            return response()->json(['message' => "Customers imported successfully. Total inserted: $insertedCount"], 200);
        } else {
            return response()->json([
                'message' => "Import completed with errors. Total inserted: $insertedCount",
                'errors'  => $errors,
            ], 400);
        }
    }

    public function updateProductVariants(Request $request)
    {
        // Validate the request
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv',
        ]);

        // Import the file
        try {
            Excel::import(new ProductVariantsImport, $request->file('file'));

            return response()->json(['message' => 'Product variants updated successfully.'], 200);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
