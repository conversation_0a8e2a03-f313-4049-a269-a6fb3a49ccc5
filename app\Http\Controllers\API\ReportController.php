<?php

namespace App\Http\Controllers\API;
use App\Exports\CustomersSummaryReportExport;
use App\Exports\PaymentsReportExport;
use App\Exports\PaymentsSummaryExport;
use App\Exports\ProfitLossReportExport;
use App\Exports\RegisterLogsReportExport;
use App\Exports\SalesPurchasesExport;
use App\Exports\SalesSummaryReportExport;
use App\Exports\StockAdjustmentExport;
use App\Exports\SuppliersSummaryReportExport;
use App\Exports\TaxReportExport;
use App\Exports\SalesDetailsReportExport;
use App\Exports\GrnSalesDetailsReportExport;
use App\Exports\PurchaseDetailsReportExport;
use App\Exports\GRNReportExport;
use App\Exports\StockReport;
use App\Exports\StudentComboReport;
use App\Libraries\AllSettingFormat;
use App\Libraries\searchHelper;
use App\Models\Branch;
use App\Models\CashRegister;
use App\Models\CashRegisterLog;
use App\Models\Customer;
use App\Models\CustomerGroup;
use App\Models\Order;
use App\Models\OrderItems;
use App\Models\Payments;
use App\Models\PaymentType;
use App\Models\Product;
use App\Models\ProductBrand;
use App\Models\ProductCategory;
use App\Models\ProductGroup;
use App\Models\ProductVariant;
use App\Models\ProductCourse;
use App\Models\ShippingInformation;
use App\Models\Supplier;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\AdjustProductStockType;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use App\Models\CustomUser;
use App\Models\Role;
use Illuminate\Support\Facades\Lang;
use PDF;
use File;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use App\Libraries\Email;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\API\PermissionController;
use App\Models\CustomerPayment;
use Illuminate\Support\Facades\Auth;
 

class ReportController extends Controller
{

    private function compare($a, $b)
    {
        return strcmp($a->sub_total, $b->sub_total);
    }

    public function salesReport(Request $request)
    {
        
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;

        //  DB::enableQueryLog();
        $sales = OrderItems::salesItems($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        //  dd(DB::getQueryLog());
        
        if (empty($requestType)) {
            $salesData = $sales['data'];
        } else {
            $salesData = $sales;
        }

        if (empty($requestType)) {
            $salesItems = $this->calculateSales($salesData);

            $arrayCount = $salesItems['count'];
            $totalCount = count($sales['allData']);
            //$item_purchased =  number_format($salesItems['netItem'],2);
            $item_purchased = round($salesItems['netItem'],2);
            $salesData[$arrayCount] = [
                'invoice_id' => Lang::get('lang.total'),
                // 'item_purchased' => number_format($item_purchased,2),
                'item_purchased' => (float)number_format($item_purchased, 2, '.', ''),
                // 'item_purchased' => $salesItems['netItem'],
                'tax' => $salesItems['netTax'],
                'discount' => $salesItems['discount'],
                'total' => $salesItems['netTotal'],
                'due_amount' => $salesItems['netDueAmount']
            ];

            $grandCalculation = $this->calculateSales($sales['allData']);

            $salesData[$arrayCount + 1] = [
                'invoice_id' => Lang::get('lang.grand_total'),
                'item_purchased' => number_format($grandCalculation['netItem'],2),
                'tax' => $grandCalculation['netTax'],
                'discount' => round($grandCalculation['discount'],2),
                'total' => $grandCalculation['netTotal'],
                'due_amount' => $grandCalculation['netDueAmount']
            ];

            $sales['allData'][] = [
                'invoice_id' => Lang::get('lang.grand_total'),
                'item_purchased' => number_format($grandCalculation['netItem'],2),
                'tax' => $grandCalculation['netTax'],
                'discount' => round($grandCalculation['discount'],2),
                'total' => round($grandCalculation['netTotal'], 2),
                'due_amount' => $grandCalculation['netDueAmount']
            ];

            return ['datarows' => $salesData, 'count' => $totalCount, 'all_data' => $sales['allData'] ];
        } else {
            $this->calculateSales($salesData);
            return ['datarows' => $salesData];
        }
    }

    public function allStockMovementRegisterLogs(Request $request)
    {
        // Export functionality
        if ($request->has('export') && $request->export == true) {
            return \Excel::download([], 'stock-movement-register.xlsx');
        }

        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = $request->searchValue;
        $requestType = $request->reqType;
        
        return ['datarows' => [], 'count' => 0,  'all_data' => []];
       
    } 
    
    public function allPurchaseDetails(Request $request)
    {
        // Export functionality
        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new PurchaseDetailsReportExport(), 'purchase-details.xlsx');
        }

        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = $request->searchValue;
        $requestType = $request->reqType;
       
        // \DB::enableQueryLog();
        $sales = OrderItems::getAllPurchaseDetails($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        // dd(\DB::getQueryLog());
      
        $subtotalRecordsLimit = $limit + $request->rowOffset;
        $Subsales = OrderItems::getAllPurchaseDetails($filtersData, $searchValue, $request->columnSortedBy, $subtotalRecordsLimit, 0, $columnName, $requestType);

        if (empty($requestType)) {
            $salesData = $sales['data'];
            $salesSubData = $Subsales['data'];
        } else {
            $salesData = $sales;
            $salesSubData = $Subsales;
        }

        if (empty($requestType)) {
            $salesItems = $this->calculateSalesDetails($salesData);

            $arrayCount = $salesItems['count'];
            $totalCount = count($sales['allData']);
           
            $subTotalVal = 0;
            $subQuantityVal = 0;
            foreach($salesSubData as $allData){         
                $subQuantityVal_temp = number_format($allData['quantity'],2);
                $subQuantityVal += (float)$subQuantityVal_temp;
            
            }
            $salesData[$arrayCount] = [
                'invoice_id' => Lang::get('lang.total'),
                'sub_total' => $subTotalVal,
                'quantity' => number_format($subQuantityVal,2),
            ];

            $grandCalculation = $this->calculateGrnSalesDetails($sales['allData']);
            
            $salesData[$arrayCount + 1] = [
                'invoice_id' => Lang::get('lang.grand_total'),
                'sub_total' => $grandCalculation['sub_total'],
                'quantity' => $grandCalculation['quantity'],
            ];

            $sales['allData'][] = [
                'invoice_id' => Lang::get('lang.grand_total'),
                'sub_total' => $grandCalculation['sub_total'],
                'quantity' => $grandCalculation['quantity'],
            ];

            return ['datarows' => $salesData, 'count' => $totalCount,  'all_data' => $sales['allData']];
        } else {
            $this->calculateGrnSalesDetails($salesData);
            return ['datarows' => $salesData];
        }
    }  

    public function allGrnDetails(Request $request)
    {
        

        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = $request->searchValue;
        $requestType = $request->reqType;

        // Export functionality
        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new GrnSalesDetailsReportExport($filtersData,$searchValue), 'grn-details.xlsx');
         } 
       
        // \DB::enableQueryLog();
        $sales = OrderItems::getAllGrnSalesDetails($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        // dd(\DB::getQueryLog());
      
        $subtotalRecordsLimit = $limit + $request->rowOffset;
        $Subsales = OrderItems::getAllGrnSalesDetails($filtersData, $searchValue, $request->columnSortedBy, $subtotalRecordsLimit, 0, $columnName, $requestType);

        if (empty($requestType)) {
            $salesData = $sales['data'];
            $salesSubData = $Subsales['data'];
        } else {
            $salesData = $sales;
            $salesSubData = $Subsales;
        }

        if (empty($requestType)) {
            $salesItems = $this->calculateSalesDetails($salesData);

            $arrayCount = $salesItems['count'];
            $totalCount = count($sales['allData']);
           
            $subTotalVal = 0;
            $subQuantityVal = 0;
            foreach($salesSubData as $allData){         
                $subQuantityVal_temp = number_format($allData['quantity'],2);
                $subQuantityVal += (float)$subQuantityVal_temp;
            
            }
            $salesData[$arrayCount] = [
                'invoice_id' => Lang::get('lang.total'),
                'sub_total' => $subTotalVal,
                'quantity' => number_format($subQuantityVal,2),
            ];

            $grandCalculation = $this->calculateGrnSalesDetails($sales['allData']);
            
            $salesData[$arrayCount + 1] = [
                'invoice_id' => Lang::get('lang.grand_total'),
                'sub_total' => $grandCalculation['sub_total'],
                'quantity' => $grandCalculation['quantity'],
            ];

            $sales['allData'][] = [
                'invoice_id' => Lang::get('lang.grand_total'),
                'sub_total' => $grandCalculation['sub_total'],
                'quantity' => $grandCalculation['quantity'],
            ];

            return ['datarows' => $salesData, 'count' => $totalCount,  'all_data' => $sales['allData']];
        } else {
            $this->calculateGrnSalesDetails($salesData);
            return ['datarows' => $salesData];
        }
    }    
    
    public function calculateGrnSalesDetails($salesData)
    {
        $netTotal = 0;
        $netItem = 0;
        $arrayCount = 0;

        foreach ($salesData as $rowData) {
            $netTotal += (float) $rowData->sub_total;
            $netItem_temp =  number_format((float)$rowData->quantity,2);
            $netItem += (float)$netItem_temp;
            $arrayCount++;
        }

        return [
            'sub_total' => $netTotal,
            'quantity' => number_format($netItem,2),
            'count' => $arrayCount,
        ];
    }

    public function allSalesDetails(Request $request)
    {
          // Export functionality
        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new SalesDetailsReportExport(), 'sales.xlsx');
        }
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = $request->searchValue;
        $requestType = $request->reqType;
       
        // \DB::enableQueryLog();
        $sales = OrderItems::getAllSalesDetails($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        // dd(\DB::getQueryLog());
      
        $subtotalRecordsLimit = $limit + $request->rowOffset;
        $Subsales = OrderItems::getAllSalesDetails($filtersData, $searchValue, $request->columnSortedBy, $subtotalRecordsLimit, 0, $columnName, $requestType);

        if (empty($requestType)) {
            $salesData = $sales['data'];
            $salesSubData = $Subsales['data'];
        } else {
            $salesData = $sales;
            $salesSubData = $Subsales;
        }

        

        if (empty($requestType)) {
            $salesItems = $this->calculateSalesDetails($salesData);

            $arrayCount = $salesItems['count'];
            $totalCount = count($sales['allData']);
           
            $subTotalVal = 0;
            $subQuantityVal = 0;
            foreach($salesSubData as $allData){         
                // $subQuantityVal += $allData['quantity'];
                $subQuantityVal_temp = number_format($allData['quantity'],2);
                $subQuantityVal += $subQuantityVal_temp;
                $subTotalVal    += $allData['sub_total'];
            }
           

            // $salesData[$arrayCount] = [
            //     'invoice_id' => Lang::get('lang.total'),
            //     'sub_total' => $salesItems['sub_total'],
            //     'quantity' => $salesItems['quantity'],
            // ];
            $salesData[$arrayCount] = [
                'invoice_id' => Lang::get('lang.total'),
                'sub_total' => $subTotalVal,
                'quantity' => number_format($subQuantityVal,2),
            ];

            
           
            $grandCalculation = $this->calculateSalesDetails($sales['allData']);
            
            $salesData[$arrayCount + 1] = [
                'invoice_id' => Lang::get('lang.grand_total'),
                'sub_total' => $grandCalculation['sub_total'],
                'quantity' => $grandCalculation['quantity'],
            ];

            $sales['allData'][] = [
                'invoice_id' => Lang::get('lang.grand_total'),
                'sub_total' => $grandCalculation['sub_total'],
                'quantity' => $grandCalculation['quantity'],
            ];

           

            return ['datarows' => $salesData, 'count' => $totalCount,  'all_data' => $sales['allData']];
        } else {
            // $this->calculateSales($salesData); // commented by srini
            $this->calculateSalesDetails($salesData); // added by srini
            return ['datarows' => $salesData];
        }

    }

    public function calculateSalesDetails($salesData)
    {

        $netTotal = 0;
        $netItem = 0;
        $arrayCount = 0;

        foreach ($salesData as $rowData) {
            $netTotal += (float)$rowData->sub_total;
            $netItem_temp =  number_format((float)$rowData->quantity,2);
            $netItem += (float)$netItem_temp;
            $arrayCount++;
        }

        return [
            'sub_total' => $netTotal,
            'quantity' => number_format($netItem,2),
            'count' => $arrayCount,
        ];
    }

    public function calculateSales($salesData)
    {
        $netTotal = 0;
        $netTax = 0;
        $netItem = 0;
        $arrayCount = 0;
        $netDiscount = 0;
        $netDueAmount = 0;

        foreach ($salesData as $rowData) {
            if ($rowData->type == 'customer') {
                $rowData->type = Lang::get('lang.customer');
            } else if ($rowData->type == 'returns') {
                $rowData->type = Lang::get('lang.returns');
            } else if ($rowData->type == 'internal-transfer') {
                $rowData->type = Lang::get('lang.internal_transfer');
            } else {
                $rowData->type = Lang::get('lang.internal_sales');
                $rowData->customer = $rowData->transfer_branch_name;
            }
            if ($rowData->customer == '') $rowData->customer = Lang::get('lang.walk_in_customer');
            $netTax += $rowData->tax;
            $netTotal += $rowData->total;
            $netItem += round($rowData->item_purchased,2);
            $netDiscount += $rowData->discount;
            $netDueAmount += $rowData->due_amount;
            $arrayCount++;
        }

        return [
            'netTotal' => $netTotal,
            'netTax' => $netTax,
            'netItem' => round($netItem,2),
            'discount' => $netDiscount,
            'count' => $arrayCount,
            'netDueAmount' => $netDueAmount
        ];
    }

    public function salesSummaryReport(Request $request)
    {
         // Export functionality
         if ($request->has('export') && $request->export == true) {
            return \Excel::download(new SalesSummaryReportExport(), 'SalesSummaryReport.xlsx');
        }
        $filterKey = 'product_brands.name as filter_key';
        $groupBy = 'products.brand_id';
        $joinTable = 'product_brands';
        $joinColumn1 = 'product_brands.id';
        $joinColumn2 = 'products.brand_id';
        $branchId = 0;
        $dateFormat = false;
        $requestType = $request->reqType;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $columnName = 'product_brands.name';
        $columnSortedBy = $request->columnSortedBy;

        if (empty($filtersData)) {
            // DB::enableQueryLog();
            $summary = OrderItems::salesSummary($filterKey, $limit, $request->rowOffset, $groupBy, $requestType, $columnName, $columnSortedBy);

            // dd(DB::getQueryLog());
        } else {
            foreach ($filtersData as $singleFilter) {
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "type") {
                    $filter = $singleFilter['value'];
                    if ($filter == 'brand') {
                        $filterKey = 'product_brands.name as filter_key';
                        $groupBy = 'products.' . $filter . '_id';
                        $joinTable = 'product_brands';
                        $joinColumn1 = 'product_brands.id';
                        $joinColumn2 = 'products.brand_id';
                        $columnName = 'product_brands.name';
                    } else if ($filter == 'category') {
                        $filterKey = 'product_categories.name as filter_key';
                        $groupBy = 'products.' . $filter . '_id';
                        $joinTable = 'product_categories';
                        $joinColumn1 = 'product_categories.id';
                        $joinColumn2 = 'products.category_id';
                        $columnName = 'product_categories.name';
                    } else if ($filter == 'group') {
                        $filterKey = 'product_groups.name as filter_key';
                        $groupBy = 'products.' . $filter . '_id';
                        $joinTable = 'product_groups';
                        $joinColumn1 = 'product_groups.id';
                        $joinColumn2 = 'products.group_id';
                        $columnName = 'product_groups.name';
                    } else if ($filter == 'customer') {
                        $groupBy = 'customers.first_name';
                        $filterKey = DB::raw('concat(customers.first_name," ",customers.last_name) as filter_key');
                        $joinTable = 'customers';
                        $joinColumn1 = 'customers.id';
                        $joinColumn2 = 'orders.customer_id';
                        $columnName = 'customers.first_name';
                    } else if ($filter == 'employee') {
                        $filterKey = DB::raw('concat(users.first_name," ",users.last_name) as filter_key');
                        $groupBy = 'orders.created_by';
                        $joinTable = 'users';
                        $joinColumn1 = 'users.id';
                        $joinColumn2 = 'orders.created_by';
                        $columnName = 'users.first_name';
                    } else if ($filter == 'product') {
                        $filterKey = DB::raw('concat(title,if(variant_title="default_variant","",concat("(",product_variants.variant_title,")"))) as filter_key');
                        $groupBy = 'order_items.variant_id';
                        $joinTable = 'product_variants';
                        $joinColumn1 = 'product_variants.id';
                        $joinColumn2 = 'order_items.variant_id';
                        $columnName = 'products.title';
                    } else if ($filter == 'date') {
                        $filterKey = 'orders.date as filter_key';
                        $groupBy = 'orders.date';
                        $dateFormat = true;
                        $columnName = 'orders.date';
                    }
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "branch") {
                    $branchId = $singleFilter['value'];
                }
            }

            $starts = 0;
            $ends = 0;
            foreach ($filtersData as $singleFilter) {
                if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                    $starts = $singleFilter['value'][0]['start'];
                    $ends = $singleFilter['value'][0]['end'];
                }
            }
             // DB::enableQueryLog();
            $summary = OrderItems::salesSummaryTypeFilter($filterKey, $limit, $request->rowOffset, $joinTable, $joinColumn1, $joinColumn2, $groupBy, $singleFilter, $branchId, $starts, $ends, $requestType, $columnName, $columnSortedBy);

            $subtotalRecordsLimit = $limit + $request->rowOffset;
           // $_summary = OrderItems::salesSummaryTypeFilter($filterKey, $subtotalRecordsLimit, 0, $joinTable, $joinColumn1, $joinColumn2, $groupBy, $singleFilter, $branchId, $starts, $ends, $requestType, $columnName, $columnSortedBy);
           
             // dd(DB::getQueryLog());
        }

        if (empty($requestType)) {
            $summaryData = $summary['data'];
          //  $_summaryData = $_summary['data'];
        } else {
            $summaryData = $summary;
          //  $_summaryData = $_summary;
        }

        foreach ($summaryData as $rowData) {
            if ($rowData->filter_key == '') $rowData->filter_key = Lang::get('lang.walk_in_customer');
            if ($dateFormat) {
                $allSettingFormat = new AllSettingFormat;
                $rowData->filter_key = $allSettingFormat->getDate($rowData->filter_key);
            }
        }
        if (empty($requestType)) {
            $totalCount = $summary['count'];
            $salesSummary = $this->calculateSalesSummary($summaryData);
            $arrayCount = $salesSummary['count'];
            $summaryData[$arrayCount] = ['filter_key' => Lang::get('lang.total'), 'item_purchased' => $salesSummary['netItem'], 'discount' => $salesSummary['discount'], 'sub_total' => $salesSummary['netSubTotal'], 'tax' => $salesSummary['netTax'], 'total' => $salesSummary['netTotal']];

       
            $grandCalculation = $this->calculateSalesSummary($summary['allData']);
            $summaryData[$arrayCount + 1] = ['filter_key' => Lang::get('lang.grand_total'), 'item_purchased' => $grandCalculation['netItem'], 'discount' => $grandCalculation['discount'], 'sub_total' => $grandCalculation['netSubTotal'], 'tax' => $grandCalculation['netTax'], 'total' => $grandCalculation['netTotal']];

            $summary['allData'][]  = ['filter_key' => Lang::get('lang.grand_total'), 'item_purchased' => $grandCalculation['netItem'], 'discount' => $grandCalculation['discount'], 'sub_total' => $grandCalculation['netSubTotal'], 'tax' => $grandCalculation['netTax'], 'total' => $grandCalculation['netTotal']];
            return ['datarows' => $summaryData, 'count' => $totalCount,  'all_data' => $summary['allData']];
        } else {

            $this->calculateSalesSummary($summaryData);
            return ['datarows' => $summaryData];
        }
    }

    public function calculateSalesSummary($salesSummary)
    {

        $netSubTotal = 0;
        $netTotal = 0;
        $netTax = 0;
        $netItem = 0;
        $arrayCount = 0;
        $netDiscount = 0;

        foreach ($salesSummary as $rowData) {

            $rowData->total = $rowData->sub_total + $rowData->tax - $rowData->discount;
            $netTax += $rowData->tax;
            $netTotal += $rowData->total;
            $netItem += $rowData->item_purchased;
            $netDiscount += $rowData->discount;
            $arrayCount++;
            $netSubTotal += $rowData->sub_total;
        }

        return ['netTotal' => $netTotal, 'netTax' => $netTax, 'netItem' => $netItem, 'discount' => $netDiscount, 'netSubTotal' => $netSubTotal, 'count' => $arrayCount];
    }


    public function getSalesDetails(Request $request, $id)
    {
        $count = 0;
        $details = OrderItems::getOrderDetails($id);
        foreach ($details as $item) {
            if ($item->title == 'Discount') {
                $item->price = null;
                $item->quantity = null;
                $item->discount = null;
            } else {
                $item->discount = $this->formateDiscount($item->discount);
                $item->price = $this->formateDiscount($item->price);
            }
            $count++;
        }

        $orders = Order::getsOrders($id);

        $details[$count++] = ['title' => Lang::get('lang.sub_total'), 'total' => $orders->sub_total];
        $details[$count++] = ['title' => Lang::get('lang.tax'), 'total' => $orders->total_tax];
        $details[$count++] = ['title' => Lang::get('lang.total'), 'total' => $orders->total];

        $payments = Payments::paymentDetails($id);

        foreach ($payments as $payment) {
            $details[$count++] = ['title' => $payment->name, 'total' => $payment->paid];
        }
        return ['datarows' => $details, 'count' => 0];
    }

    public function formateDiscount($discount)
    {
        $allSettingFormat = new AllSettingFormat;
        return $allSettingFormat->getCurrencySeparator($discount);
    }

    public function calculateOrderDetails($orderDetails)
    {
        $totalQuantity = 0;
        $subTotal = 0;
        $totalTax = 0;
        $allTotal = 0;
        $count = 0;
        foreach ($orderDetails as $item) {

            if ($item->tax != null) {
                $taxAmount = $item->calculatingTax;
            } else {
                $taxAmount = 0;
            }
            $tax = ($item->subtotal * $taxAmount) / 100;

            if ($item->discount_type == "%") {
                $discount = ($item->subTotal * $item->discount) / 100;
            } else {
                $discount = $item->discount;
            }
            $item->total = ($item->subtotal - $discount) + $taxAmount;
            $count++;
            $totalQuantity = $totalQuantity + $item->quantity;
            $subTotal = $subTotal + $item->subtotal;
            $totalTax = $totalTax + $taxAmount;
            $allTotal = $allTotal + $item->total;
        }
        return ['totalQuantity' => $totalQuantity, 'subTotal' => $subTotal, 'totalTax' => $totalTax, 'allTotal' => $allTotal, 'count' => $count];
    }

    public function purchaseReport(Request $request)
    {
        if ($request->columnKey) $columnName = $request->columnKey;

        if ($request->rowLimit) $limit = $request->rowLimit;
        $requestType = $request->reqType;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $receiving = OrderItems::receivingItems($filtersData, $searchValue, $columnName, $request->columnSortedBy, $limit, $request->rowOffset, $requestType);

        if (empty($requestType)) {
            $receivingItems = $receiving['data'];
            $totalCalculation = $this->calculateReceivings($receiving['data']);
            $arrayCount = $totalCalculation['count'];
            $totalCount = count($receiving['allData']);
            $receivingItems[$arrayCount] = ['invoice_id' => Lang::get('lang.total'), 'item_purchased' => $totalCalculation['netItem'], 'total' => $totalCalculation['total'], 'due_amount' => $totalCalculation['netDue']];

            $grandCalculation = $this->calculateReceivings($receiving['allData']);

            $receivingItems[$arrayCount + 1] = ['invoice_id' => Lang::get('lang.grand_total'), 'item_purchased' => $grandCalculation['netItem'], 'total' => $grandCalculation['total'], 'due_amount' => $totalCalculation['netDue']];

            $receiving['allData'][] = ['invoice_id' => Lang::get('lang.grand_total'), 'item_purchased' => $grandCalculation['netItem'], 'total' => $grandCalculation['total'], 'due_amount' => $totalCalculation['netDue']];

            return ['datarows' => $receivingItems, 'count' => $totalCount, 'all_data' =>  $receiving['allData']];
        } else {

            $this->calculateReceivings($receiving);
            return ['datarows' => $receiving];
        }
    }

    public function grnReport(Request $request)
    {
        if ($request->columnKey) $columnName = $request->columnKey;

        if ($request->rowLimit) $limit = $request->rowLimit;
        $requestType = $request->reqType;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);


        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new GRNReportExport($filtersData,$searchValue), 'grn.xlsx');
        }

        $receiving = OrderItems::grnReceivingItems($filtersData, $searchValue, $columnName, $request->columnSortedBy, $limit, $request->rowOffset, $requestType);

        if (empty($requestType)) {
            $receivingItems = $receiving['data'];
            $totalCalculation = $this->calculateReceivings($receiving['data']);
            $arrayCount = $totalCalculation['count'];
            $totalCount = count($receiving['allData']);
            $receivingItems[$arrayCount] = ['invoice_id' => Lang::get('lang.total'), 'item_purchased' => $totalCalculation['netItem'], 'total' => $totalCalculation['total'], 'due_amount' => $totalCalculation['netDue']];

            $grandCalculation = $this->calculateReceivings($receiving['allData']);

            $receivingItems[$arrayCount + 1] = ['invoice_id' => Lang::get('lang.grand_total'), 'item_purchased' => $grandCalculation['netItem'], 'total' => $grandCalculation['total'], 'due_amount' => $totalCalculation['netDue']];

            $receiving['allData'][] = ['invoice_id' => Lang::get('lang.grand_total'), 'item_purchased' => $grandCalculation['netItem'], 'total' => $grandCalculation['total'], 'due_amount' => $totalCalculation['netDue']];

            return ['datarows' => $receivingItems, 'count' => $totalCount,];
            
        } else {

            $this->calculateReceivings($receiving);
            return ['datarows' => $receiving];
        }
    }

    public function calculateReceivings($receivingData)
    {
        $netTotal = 0;
        $netItem = 0;
        $netDue = 0;
        $arrayCount = 0;

        foreach ($receivingData as $rowData) {

            if ($rowData->type == 'supplier') {
                $rowData->type = Lang::get('lang.supplier');
            } else if ($rowData->type == 'internal-transfer') {
                $rowData->type = Lang::get('lang.internal_transfer');
            } else {
                $rowData->type = Lang::get('lang.internal_receivings');
            }

            $netTotal += $rowData->total;
            $netItem += $rowData->item_purchased;
            $netDue += $rowData->due_amount;
            $arrayCount++;
        }

        return ['total' => $netTotal, 'netItem' => $netItem, 'netDue' => $netDue, 'count' => $arrayCount];
    }

    public function receivingSummary(Request $request)
    {
        $filterKey = 'product_brands.name as filter_key';
        $groupBy = 'products.brand_id';
        $joinTable = 'product_brands';
        $joinColumn1 = 'product_brands.id';
        $joinColumn2 = 'products.brand_id';
        $branchId = 0;
        $dateFormat = false;
        $requestType = $request->reqType;

        if ($request->columnKey) $columnName = $request->columnKey;

        if ($request->rowLimit) $limit = $request->rowLimit;

        $filtersData = $request->filtersData;
        $columnName = 'product_brands.name';
        $columnSortedBy = $request->columnSortedBy;

        if (empty($filtersData)) {
            $summaryData = OrderItems::receiveSummary($filterKey, $limit, $request->rowOffset, $groupBy, $requestType, $columnName, $columnSortedBy);
        } else {

            foreach ($filtersData as $singleFilter) {
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "type") {
                    $filter = $singleFilter['value'];

                    if ($filter == 'brand') {
                        $filterKey = 'product_brands.name as filter_key';
                        $groupBy = 'products.' . $filter . '_id';
                        $joinTable = 'product_brands';
                        $joinColumn1 = 'product_brands.id';
                        $joinColumn2 = 'products.brand_id';
                        $columnName = 'product_brands.name';
                    }

                    if ($filter == 'category') {
                        $filterKey = 'product_categories.name as filter_key';
                        $groupBy = 'products.' . $filter . '_id';
                        $joinTable = 'product_categories';
                        $joinColumn1 = 'product_categories.id';
                        $joinColumn2 = 'products.category_id';
                        $columnName = 'product_categories.name';
                    }

                    if ($filter == 'group') {
                        $filterKey = 'product_groups.name as filter_key';
                        $groupBy = 'products.' . $filter . '_id';
                        $joinTable = 'product_groups';
                        $joinColumn1 = 'product_groups.id';
                        $joinColumn2 = 'products.group_id';
                        $columnName = 'product_groups.name';
                    }

                    if ($filter == 'supplier') {
                        $filterKey = DB::raw('concat(suppliers.first_name," ",suppliers.last_name) as filter_key');
                        $groupBy = 'suppliers.first_name';
                        $joinTable = 'suppliers';
                        $joinColumn1 = 'suppliers.id';
                        $joinColumn2 = 'orders.supplier_id';
                        $columnName = 'suppliers.first_name';
                    }

                    if ($filter == 'employee') {
                        $filterKey = DB::raw('concat(users.first_name," ",users.last_name) as filter_key');
                        $groupBy = 'orders.created_by';
                        $joinTable = 'users';
                        $joinColumn1 = 'users.id';
                        $joinColumn2 = 'orders.created_by';
                        $columnName = 'users.first_name';
                    }

                    if ($filter == 'product') {
                        $filterKey = DB::raw('concat(title,if(variant_title="default_variant","",concat("(",product_variants.variant_title,")"))) as filter_key');
                        $groupBy = 'order_items.variant_id';
                        $joinTable = 'product_variants';
                        $joinColumn1 = 'product_variants.id';
                        $joinColumn2 = 'order_items.variant_id';
                        $columnName = 'products.title';
                    }

                    if ($filter == 'date') {
                        $filterKey = 'orders.date as filter_key';
                        $groupBy = 'orders.date';
                        $dateFormat = true;
                        $columnName = 'orders.date';
                    }
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "branch") {
                    $branchId = $singleFilter['value'];
                }
            }

            $starts = 0;
            $ends = 0;

            foreach ($filtersData as $singleFilter) {
                if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                    $starts = $singleFilter['value'][0]['start'];
                    $ends = $singleFilter['value'][0]['end'];
                }
            }

            $summaryData = OrderItems::receiveSummaryFilter($filterKey, $limit, $request->rowOffset, $joinTable, $joinColumn1, $joinColumn2, $groupBy, $singleFilter, $branchId, $starts, $ends, $requestType, $columnName, $columnSortedBy);
        }

        if (empty($requestType)) {
            $receiveSummary = $summaryData['data'];
        } else {
            $receiveSummary = $summaryData;
        }

        foreach ($receiveSummary as $rowQuery) {
            if ($rowQuery->filter_key == '') $rowQuery->filter_key = Lang::get('lang.walk_in_supplier');

            if ($dateFormat) {
                $allSettingFormat = new AllSettingFormat;
                $rowQuery->filter_key = $allSettingFormat->getDate($rowQuery->filter_key);
            }
        }

        if (empty($requestType)) {

            $totalCalculation = $this->calculateReceivingSummary($receiveSummary);
            $arrayCount = $totalCalculation['count'];
            $totalCount = $summaryData['count'];
            $receiveSummary[$arrayCount] = ['filter_key' => Lang::get('lang.total'), 'item_receive' => $totalCalculation['netItem'], 'total' => $totalCalculation['total']];
            $grandCalculation = $this->calculateReceivingSummary($summaryData['allData']);
            $receiveSummary[$arrayCount + 1] = ['filter_key' => Lang::get('lang.grand_total'), 'item_receive' => $grandCalculation['netItem'], 'total' => $grandCalculation['total']];

            return ['datarows' => $receiveSummary, 'count' => $totalCount];
        } else {
            $this->calculateReceivingSummary($receiveSummary);
            return ['datarows' => $receiveSummary];
        }
    }

    public function calculateReceivingSummary($receivingData)
    {

        $netTotal = 0;
        $netItem = 0;
        $arrayCount = 0;

        foreach ($receivingData as $rowData) {
            $rowData->total = $rowData->sub_total + $rowData->tax;
            $netTotal += $rowData->total;
            $netItem += $rowData->item_receive;
            $arrayCount++;
        }

        return ['total' => $netTotal, 'netItem' => $netItem, 'count' => $arrayCount];
    }


    public function registerLogReports(Request $request)
    {
        // Export functionality
        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new RegisterLogsReportExport(), 'CashRegisterLogReport.xlsx');
        }
        if ($request->columnKey) $columnName = $request->columnKey;

        if ($request->rowLimit) $limit = $request->rowLimit;

        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;
        $registerLogData = CashRegisterLog::registerLogFilter($filtersData, $searchValue, $columnName, $request->columnSortedBy, $limit, $request->rowOffset, $requestType);
        if (empty($requestType)) {

            $registerLogs = $registerLogData['data'];
        } else {
            $registerLogs = $registerLogData;
        }

        foreach ($registerLogs as $registerLog) {

            $paymentInfo = Payments::getPaymentInfo($registerLog->cash_register_id, $registerLog->opening_time, $registerLog->closing_time);

            $registerLog->cash_receives = $paymentInfo['receiving'];
            $registerLog->cash_sales = $paymentInfo['sales'];


            if ($registerLog->difference == null) {
                $registerLog->difference = '';
            }

            if ($registerLog->closing_amount == null) {
                $registerLog->closing_amount = '';
            }

            if ($registerLog->closed_by) {
                //$registerLog->difference = (float)$registerLog->opening_amount + (float)$registerLog->cash_sales - (float)$registerLog->cash_receives - (float)$registerLog->closing_amount - (float)$registerLog->expense_amount;
                $registerLog->difference = (float)$registerLog->opening_amount + (float)$registerLog->cash_sales  - (float)$registerLog->closing_amount - (float)$registerLog->expense_amount;
                $registerLog->closed_user = CustomUser::userInfo($registerLog->closed_by); 
            }

            if ($registerLog->status == 'closed') {
                $registerLog->status = Lang::get('lang.closed');
            } else {
                $registerLog->status = Lang::get('lang.open');
                $registerLog->closing_amount = '';
            }
        }
        if (empty($requestType)) {
            $totalCount = $registerLogData['count'];

            return ['datarows' => $registerLogs, 'count' => $totalCount, 'all_data' => $registerLogData['allData']];
        } else {
            return ['datarows' => $registerLogData];
        }
    }

    public function getCashRegisterFilterData()
    {

        $cashRegisters = CashRegister::index(['title as text', 'id as value']);
        $user = CustomUser::getAll([DB::raw('concat(first_name," ",last_name) as text'), 'id as value'], 'user_type', 'staff');
        $branch = Branch::index(['name as text', 'id as value']);

        $perm       = new PermissionController();
        $permission = $perm->canEditCashLogPermission();

        return ['user' => $user, 'branch' => $branch, 'cashRegisters' => $cashRegisters, 'permission' => $permission];
    }

    public function inventoryReports(Request $request)
    {
        if ($request->columnKey) $columnName = $request->columnKey;

        if ($request->rowLimit) $limit = $request->rowLimit;

        $offset = $request->rowOffset;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;
        $inventory = ProductVariant::inventoryReports($filtersData, $searchValue, $columnName, $request->columnSortedBy, $limit, $offset, $requestType);
        $inventories = $inventory['data'];
        foreach ($inventories as $item) {

            if ($item->variantTitle == 'default_variant') {
                $item->variantTitle = '';
            }
        }
        if (empty($requestType)) {
            $inventories = $inventory['data'];
        } else {
            $inventories = $inventory;
        }

        if (empty($requestType)) {
            $inventoryItems = $this->calculateInventory($inventories);

            $arrayCount = $inventoryItems['count'];
            $totalCount = count($inventory['allData']);
            $inventories[$arrayCount] = [
                'id' => Lang::get('lang.total'),
                'purchase_price' => $inventoryItems['netPurchasePrice'],
                'selling_price' => $inventoryItems['netSellingPrice'],
                'inventory' => $inventoryItems['netInventory'],
            ];

            $grandCalculation = $this->calculateInventory($inventory['allData']);

            $inventories[$arrayCount + 1] = [
                'id' => Lang::get('lang.grand_total'),
                'purchase_price' => $grandCalculation['netPurchasePrice'],
                'selling_price' => $grandCalculation['netSellingPrice'],
                'inventory' => $grandCalculation['netInventory'],
            ];

            $inventory['allData'][] =   [
                'id' => Lang::get('lang.grand_total'),
                'purchase_price' => $grandCalculation['netPurchasePrice'],
                'selling_price' => $grandCalculation['netSellingPrice'],
                'inventory' => $grandCalculation['netInventory'],
            ];

            return ['datarows' => $inventories, 'count' => $totalCount, 'all_data' => $inventory['allData']];
        } else {
            $this->calculateInventory($inventories);
            return ['datarows' => $inventories];
        }

    }

    public function calculateInventory($inventories)
    {
        $netPurchasePrice = 0;
        $netSellingPrice = 0;
        $arrayCount = 0;
        $netInventory = 0;

        foreach ($inventories as $rowData) {
            if ($rowData->type == 'customer') {
                $rowData->type = Lang::get('lang.customer');
            } else {
                $rowData->type = Lang::get('lang.internal_sales');
                $rowData->customer = $rowData->transfer_branch_name;
            }
            if ($rowData->customer == '') $rowData->customer = Lang::get('lang.walk_in_customer');
            $netPurchasePrice += $rowData->purchase_price;
            $netSellingPrice += $rowData->selling_price;
            $netInventory += $rowData->inventory;
            $arrayCount++;
        }

        return [
            'netPurchasePrice' => $netPurchasePrice,
            'netSellingPrice' => $netSellingPrice,
            'netInventory' => $netInventory,
            'count' => $arrayCount,
        ];
    }

    public function inventoryReportsFilter()
    {
        $branchName = Branch::index(['name as text', 'id as value']);
        $brandName = ProductBrand::index(['name as text', 'id as value']);
        $categoryName = ProductCategory::index(['name as text', 'id as value']);
        $groupName = ProductGroup::index(['name as text', 'id as value']);

        return ['branchName' => $branchName, 'brandName' => $brandName, 'categoryName' => $categoryName, 'groupName' => $groupName];
    }

    public function paymentReport(Request $request)
    {
          // Export functionality
          if ($request->has('export') && $request->export == true) {
            return \Excel::download(new PaymentsReportExport(), 'PaymentsReport.xlsx');
        }
        if ($request->columnKey) $columnName = $request->columnKey;

        if ($request->rowLimit) $limit = $request->rowLimit;

        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;
        // DB::enableQueryLog();
        $payments = Payments::paymentReportList($filtersData, $searchValue, $columnName, $request->columnSortedBy, $limit, $request->rowOffset, $requestType);
        // dd(DB::getQueryLog());
        foreach ($payments['data'] as $payment) {

            if ($payment->paid_by == null) {
                $payment->paid_by = Lang::get('lang.walk_in_customer');
            }

            if ($payment->order_type == 'sales') {
                $payment->route = 'customer';
            } else {
                $payment->route = 'user';
            }
        }

        if (empty($requestType)) {
            $totalCount = $payments['count'];
            $paymentData = $payments['data'];
            $totalCalculation = $this->calculatePayment($paymentData);
            $rowCount = $totalCalculation['count'];

            $paymentData[$rowCount] = ["invoice_id" => Lang::get('lang.total'), "paid" => $totalCalculation['totalPaid'], 'change' => $totalCalculation['totalChange']];

            $grandCalculation = $this->calculatePayment($payments['allData']);

            $paymentData[$rowCount + 1] = ["invoice_id" => Lang::get('lang.grand_total'), "paid" => $grandCalculation['totalPaid'], 'change' => $grandCalculation['totalChange']];
            $payments['allData'][] =  ["invoice_id" => Lang::get('lang.grand_total'), "paid" => $grandCalculation['totalPaid'], 'change' => $grandCalculation['totalChange']];

            return ['datarows' => $paymentData, 'count' => $totalCount, 'all_data' => $payments['allData']];
        } else {
            return ['datarows' => $payments['data']];
        }
    }

    public function salesReportCron()
    {
        $payment_summary    = []; 
        $sales              = [];
        $sales              = OrderItems::salesItemsCron();
        $payment_types      = PaymentType::getPaymentTypes(); 
        $payment_summary    = Payments::paymentReportListCron(); 



        // return view('reports.reportPdfView', [ 'payments' => $sales , 'payment_types' => $payment_types, 'payment_summary' => $payment_summary ]);
        
        $subject    = 'Seehouse Fabrics - Sales Report on '.date('dMY');
        $email      = '<EMAIL>';
        $mailText   = '<html>
                        <head>
                        <title>Page Title</title>
                        </head>
                        <body>

                        <h3>Automatic Sales Report</h3>
                        <p>Hi There,<br>
                        Today Sales Report is created successfully! <br>
                        The report is now available for download to check attached document. </p>
                        <br>
                        <p>Thanks, <br>
                        Stellifyflows</p>

                        </body>
                        </html>';

       
        $emailSend = new Email;     
        $fileNameToStore = "Sales Report ".date('dMY').".pdf";
        $pdf = PDF::loadView('reports.reportPdfView', [ 'payments' => $sales , 'payment_types' => $payment_types, 'payment_summary' => $payment_summary ] )
        ->setPaper('a4', 'landscape')
        ->setWarnings(false);

        $content    = $pdf->download()->getOriginalContent();

        Storage::put('public/pdf/'.$fileNameToStore,$content);
        $url = env('APP_URL');
        $_pdf =     $url.'public/pdf/'.$fileNameToStore;
        $_pdf =     storage_path('app/public/pdf/'.$fileNameToStore);   

        $emailSend->sendEmail($mailText, $email, $subject, $fileNameToStore);
        unlink($_pdf); 

    }

    

    public function calculatePayment($paymentData)
    {
        $totalPaid = 0;
        $totalChange = 0;
        $rowCount = 0;

        foreach ($paymentData as $rowPayment) {
            $totalPaid += $rowPayment->paid;
            $totalChange += $rowPayment->change;
            $rowCount++;
        }

        return ['totalPaid' => $totalPaid, 'totalChange' => $totalChange, 'count' => $rowCount];
    }

    public function paymentSummary(Request $request)
    {

         // Export functionality
         if ($request->has('export') && $request->export == true) {
            return \Excel::download(new PaymentsSummaryExport(), 'PaymentsSummaryReport.xlsx');
        }
        if ($request->columnKey) $columnName = $request->columnKey;

        if ($request->rowLimit) $limit = $request->rowLimit;

        $filtersData = $request->filtersData;
        $total = 0;
        $requestType = $request->reqType;

        $paymentSummary = Payments::paymentSummary($filtersData, $columnName, $request->columnSortedBy, $limit, $request->rowOffset, $requestType);

        if (empty($requestType)) {
            $totalCount = $paymentSummary['count'];
            $payment = $paymentSummary['data'];
            $dateFormat = false;

            foreach ($filtersData as $singleFilter) {
                if (array_key_exists('key', $singleFilter) && $singleFilter['value'] == "date") {
                    $dateFormat = true;
                }
            }

            if (empty($filtersData) || $dateFormat) {

                foreach ($payment as $SinglePayment) {
                    $allSettingFormat = new AllSettingFormat;
                    $SinglePayment->filter_key = $allSettingFormat->getDate($SinglePayment->filter_key);
                }
            } else {
                foreach ($payment as $SinglePayment) {
                    if ($SinglePayment->filter_key == null) $SinglePayment->filter_key = Lang::get('lang.walk_in_customer');
                }
            }
            $calculateTotal = $this->calculatePaymentSummary($payment);
            $rowCount = $calculateTotal['count'];
            $payment[$rowCount] = ['filter_key' => Lang::get('lang.total'), 'total' => $calculateTotal['total']];

            $calculateGrandTotal = $this->calculatePaymentSummary($paymentSummary['allData']);

            $payment[$rowCount + 1] = ['filter_key' => Lang::get('lang.grand_total'), 'total' => $calculateGrandTotal['total']];

            $paymentSummary['allData'][] = ['filter_key' => Lang::get('lang.grand_total'), 'total' => $calculateGrandTotal['total']];
            return ['datarows' => $payment, 'count' => $totalCount, 'all_data' => $paymentSummary['allData']];
        } else {
            $this->calculatePaymentSummary($paymentSummary);
            return ['datarows' => $paymentSummary];
        }
    }

    public function calculatePaymentSummary($paymentSummary)
    {
        $total = 0;
        $count = 0;

        foreach ($paymentSummary as $value) {

            $typeArray = explode(',', $value->type);
            for ($i = 0; $i < count($typeArray); $i++) {

                if ($typeArray[$i] == 'sales') {
                    $typeArray[$i] = Lang::get('lang.sales');
                } else {
                    $typeArray[$i] = Lang::get('lang.receiving');
                }
            }

            $value->type = implode(',', $typeArray);

            $total = $total + $value->total;
            $count++;
        }

        return ['total' => $total, 'count' => $count];
    }

    public function paymentReportFilter()
    {
        $cashRegister   = CashRegister::index(['title as text', 'id as value']);
        $paymentMethod  = PaymentType::index(['name as text', 'id as value']);
            
        $perm       = new PermissionController();
        $permission = $perm->paymentTypeChangePermission();

        return ['cashRegister' => $cashRegister, 'paymentMethod' => $paymentMethod, 'permission' => $permission ];
    }

    public function paymentSummaryReportFilter()
    {
        $paymentMethod = PaymentType::index(['name as text', 'id as value']);

        return ['paymentMethod' => $paymentMethod];
    }

    public function customerPurchaseReport(Request $request, $id)
    {

        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;

        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;
 
        $customerDetails = Order::customerDetails($id, $filtersData, $searchValue, $columnName, $request->columnSortedBy, $limit, $request->rowOffset, $requestType);
  

        $customerPurchase = $customerDetails['data'];

        $total = $this->calculateCustomerPurchase($customerPurchase);

        if (empty($requestType)) {
            $count = $total['count'];
            $customerPurchase[$count] = [
                'id' => Lang::get('lang.total'),
                'item_purchased' => $total['item_purchased'],
                'sub_total' => $total['subTotal'],
                'tax' => $total['tax'],
                'discount' => $total['discount'],
                'total' => $total['total'],
                'due_amount' => $total['due_amount']
            ];

            $grandTotal = $this->calculateCustomerPurchase($customerDetails['allData']);
            $customerPurchase[$count + 1] = [
                'id' => Lang::get('lang.grand_total'),
                'item_purchased' => $grandTotal['item_purchased'],
                'sub_total' => $grandTotal['subTotal'],
                'tax' => $grandTotal['tax'],
                'discount' => $grandTotal['discount'],
                'total' => $grandTotal['total'],
                'due_amount' => $grandTotal['due_amount']
            ];
        }

        return ['datarows' => $customerPurchase, 'count' => $customerDetails['count']];
    }

    public function calculateCustomerPurchase($purchaseRecord)
    {
        $netTotal = 0;
        $netSubTotal = 0;
        $netTax = 0;
        $netDue = 0;
        $discount = 0;
        $count = 0;
        $item = 0;

        foreach ($purchaseRecord as $rowData) {
            $netTax += $rowData->tax;
            $netSubTotal += $rowData->sub_total;
            $netTotal += $rowData->total;
            $discount += $rowData->discount;
            $item += $rowData->item_purchased;
            $netDue += $rowData->due_amount;
            $count++;
        }
        return ['item_purchased' => $item, 'subTotal' => $netSubTotal, 'tax' => $netTax, 'discount' => $discount, 'total' => $netTotal, 'count' => $count, 'due_amount' => $netDue];
    }

    
    public function getOrdersDetails(Request $request, $orderId)
    {
        $branchId = Order::query()->select('branch_id', 'invoice_id')->find($orderId);
        $cashRegisterId = Branch::getCashRegisterID($branchId->branch_id);
        $transferBranchName = '';
        $allSettings = new AllSettingFormat;
        $orderDetails = Order::orderDetails($orderId, $cashRegisterId);
        $total_qty = OrderItems::where('order_id', $orderId)->sum('ordered_quantity');
        $supplier = Supplier::where('id', $orderDetails->supplier_id)->first();
        $paymentDetails = Payments::where('order_id', $orderId)->get();
        $typeOfSale = $orderDetails->sale_type;
        $spaceBasic = 0;
        $paymentMethod = env('PAYMENT_METHOD_SPACE_BASIC', 6); // fallback to 6
        if (count($paymentDetails) == 1 && $paymentDetails[0]->payment_method == $paymentMethod) {
            $latestPayment = CustomerPayment::where('customer_id', $orderDetails->customer_id)->latest()->first();
            $spaceBasic = isset($latestPayment) ? $latestPayment->paid : 0;
        }
        if ($orderDetails->transfer_branch_id != null) {
            $transferBranchName = Branch::getOne($orderDetails->transfer_branch_id)->name;
        }
        $invoiceTemplate = new InvoiceTemplateController();
        $templateData = $invoiceTemplate->getInvoiceTemplateToPrint($orderId, $orderDetails->sales_or_receiving_type, $transferBranchName, $cashRegisterId, $orderDetails->order_type, 'receipt');
        $largeInvoiceView =  $invoiceTemplate->reportInvoiceTemplateToPrint($orderId, $orderDetails->sales_or_receiving_type, $transferBranchName, $cashRegisterId, $orderDetails->order_type, 'receipt');
        if($orderDetails->order_type == "receiving"){
            $productDetails = $this->getOrderItemssummary($orderId);
            $productData = "";
            $slNO = 1;
            foreach ($productDetails['data'] as $item) {
                $productData .= "<tr>";
                $productData .= "<tr ><td class='text-center pr-0'>".$slNO++."</td>";
                $productData .= "<td>".$item->title."</td>";
                $productData .= "<td class='text-center pr-0'>".$item->short_name."</td>";
                $productData .= "<td class='text-center pr-0'> &nbsp; &nbsp;".$item->orderquantity."</td>";
                $productData .= "<td class='text-end '>".number_format($item->price, 2)."</td>";
                $productData .= "<td class='text-end '>".number_format($item->total_price, 2)."</td>";
                $productData .= "</tr>";
            }
            $templateData = str_replace('{product_details}', $productData, $templateData);
            $totalAmountInWords = currencyToWords($orderDetails->total, 'INR');
            $roundoff = round(abs(floor($orderDetails->total) - ((float)$orderDetails->total_tax + (float)$orderDetails->sub_total)), 2);
            $templateData = str_replace('{roundoff}',  number_format($roundoff, 2), $templateData);
            $total = floor($orderDetails->total);
            $templateData = str_replace('{total_sum}', $allSettings->getCurrency(number_format($total, 2)), $templateData);
            $templateData = str_replace('{tax}', $orderDetails->total_tax, $templateData);
            $templateData = str_replace('{sub_total}', number_format($orderDetails->sub_total, 2), $templateData); 

            $supplier_first_name    = isset($supplier->first_name) ? $supplier->first_name : '';
            $supplier_last_name     = isset($supplier->last_name) ? $supplier->last_name : '';
            $supplier_phone         = isset($supplier->phone_number) ? $supplier->phone_number : '';
            $supplier_name          = $supplier_first_name.$supplier_last_name;

            $templateData = str_replace('{customer_name}', $supplier_name, $templateData);
            $templateData = str_replace('{customer_phone}', $supplier_phone, $templateData);
            $templateData = str_replace('{total_qty}', $total_qty, $templateData);
            $taxPercentage = ($orderDetails->sub_total != 0) ? ($orderDetails->total_tax / $orderDetails->sub_total) * 100 : 0;
            $templateData = str_replace('{gst_percentage}', number_format($taxPercentage, 2), $templateData);
            $templateData = str_replace('{totalAmountInWords}', $totalAmountInWords, $templateData);
 
            $largeInvoiceView = str_replace('{product_details}', $productData, $largeInvoiceView);
            $roundoff = round(abs(floor($orderDetails->total) - ((float)$orderDetails->total_tax + (float)$orderDetails->sub_total)), 2);
            $total = floor($orderDetails->total);
            $largeInvoiceView = str_replace('{roundoff}',  number_format($roundoff, 2), $largeInvoiceView);
            $largeInvoiceView = str_replace('{total_sum}',  $allSettings->getCurrency(number_format($total, 2)), $largeInvoiceView);
            $largeInvoiceView = str_replace('{tax}', $orderDetails->total_tax, $largeInvoiceView);
            $largeInvoiceView = str_replace('{sub_total}', number_format($orderDetails->sub_total, 2), $largeInvoiceView);
            $largeInvoiceView = str_replace('{customer_name}', $supplier->first_name.$supplier->last_name, $largeInvoiceView);
            $largeInvoiceView = str_replace('{customer_phone}', $supplier->phone_number, $largeInvoiceView);
            $largeInvoiceView = str_replace('{gst_percentage}', number_format($taxPercentage, 2), $largeInvoiceView);
            $largeInvoiceView = str_replace('{totalAmountInWords}', $totalAmountInWords, $largeInvoiceView);
            $largeInvoiceView = str_replace('{total_qty}', $total_qty, $largeInvoiceView);
        } else {
            if ($orderDetails->branch_id == 1) {
                $templateData = preg_replace('/<tr>\s*<td>\s*Total\s*<\/td>\s*<td>.*?<\/td>\s*<\/tr>/i', '', $templateData);
            }

            if($typeOfSale == 'combo' && $spaceBasic > 0){
                $spaceBasic = $allSettings->getCurrency($allSettings->thousandSep($spaceBasic));
                $templateData = preg_replace_callback(
                    '/(<tr>\s*<td>\s*Paid in Spacebasic\s*<\/td>\s*<td>)₹[\d,.]+(<\/td>\s*<\/tr>)/i',
                    function ($matches) use ($spaceBasic) {
                        return $matches[1] . $spaceBasic . $matches[2];
                    },
                    $templateData
                );
            }
        }
        return [
            'templateData' => $templateData,
            'invoiceId' => $branchId->invoice_id,
            'largeInvoiceView' => $largeInvoiceView['data']
        ];
    }

    public function getGRNOrdersDetails(Request $request, $orderId)
    {
        $branchId = Order::query()->select('branch_id', 'invoice_id')->find($orderId);
        $order_date = Order::select('date')->where(['invoice_id' => $branchId->invoice_id, 'grn_invoice_id' => '0'])->orderBy('date')->first();
        $cashRegisterId = Branch::getCashRegisterID($branchId->branch_id);
        $transferBranchName = '';
        $orderDetails = Order::orderDetails($orderId, $cashRegisterId);

        if ($orderDetails->transfer_branch_id != null) {
            $transferBranchName = Branch::getOne($orderDetails->transfer_branch_id)->name;
        }
        $invoiceTemplate = new InvoiceTemplateController();
        $templateData = $invoiceTemplate->getInvoiceTemplateToPrint($orderId, "receiving", $transferBranchName, $cashRegisterId, 'grn', 'receipt');

        if($orderDetails->order_type == "receiving"){
            $productDetails = $this->getOrderItemssummary($orderId);
            $productData = "";
            $slNO = 1;
            foreach ($productDetails['data'] as $item) {
                $productData .= "<tr>";
                $productData .= "<tr><td>".$slNO++."</td>";
                $productData .= "<td>".$item->title."</td>";
                $productData .= "<td>".$item->short_name."</td>";
                $productData .= "<td class='text-center'>".$item->orderquantity."</td>";
                $productData .= "<td class='text-center'>".$item->receivedquantity."</td>";
                $productData .= "</tr>";
            }

            $productData = "";
            foreach ($productDetails['data'] as $item) {
                $productData .= "<tr>";
                $productData .= "<td colspan='4' style='text-align: center;'>".$item->title."</td>";
                $productData .= "</tr>";
                
                $itemsList = $this->getOrderItemssummaryDetails($orderId);
                foreach($itemsList as $rec){
                    $productData .= "<tr>";
                    $productData .= "<td >".$rec->course."</td>";
                    $productData .= "<td >".$rec->item."</td>";
                    $productData .= "<td class='text-center'>".$rec->item_size."</td>";
                    $productData .= "<td class='text-center'>".$item->receivedquantity."</td>";
                    $productData .= "</tr>";
                }
                
            }

            $templateData = str_replace('{product_details}', $productData, $templateData);
            $templateData = str_replace('{remarks}', $productDetails['order'][0]['sales_note'], $templateData);
            $templateData = str_replace('{current_date}', date("d/m/Y"), $templateData);
            $templateData = str_replace('{grninvoice_id}', $productDetails['order'][0]['grn_invoice'], $templateData);
            $templateData = str_replace('{po_date}', date('d/m/Y', strtotime($order_date->date)), $templateData);
            
            
            if($orderDetails->grn_invoice_number_ref){
                $invoiceInfo = ' <div class="info-row">
            <div class="info-label">Invoice Number</div>
            <div class="info-value">:&nbsp; &nbsp; '.$orderDetails->grn_invoice_number_ref.' &  '.date('d/m/Y', strtotime($orderDetails->grn_invoice_ref_date)).'</div>
            </div>';
                $templateData = str_replace('{invoice_info}', $invoiceInfo, $templateData); 
            }else{
                $templateData = str_replace('{invoice_info}','', $templateData); 
            }            

            $largeInvoiceView = str_replace('{product_details}', $productData, $templateData);
            $largeInvoiceView = str_replace('{current_date}', $productDetails['order'][0]['date'], $largeInvoiceView);
            $largeInvoiceView = str_replace('{grninvoice_id}', $productDetails['order'][0]['grn_invoice'], $largeInvoiceView);
            $largeInvoiceView = str_replace('{remarks}', $productDetails['order'][0]['sales_note'], $largeInvoiceView);
            $largeInvoiceView = str_replace('{po_date}', date('d/m/Y', strtotime($order_date->date)), $largeInvoiceView);
        }
        return [
            'templateData' => $templateData,
            'invoiceId' => $branchId->invoice_id,
            'largeInvoiceView' => $largeInvoiceView['data']
        ];
    }
 
   // Order Item details summary wise with out variant 03-07-2024
   public static function getOrderItemssummary($orderId)
   {
        $productDetailsQuery = DB::table('order_items as oi')
            ->join('products as p', 'oi.product_id', '=', 'p.id')
            ->join('product_categories as pc', 'p.category_id', '=', 'pc.id')
            ->join('product_units as pu','p.unit_id', '=', 'pu.id')
            ->where('oi.order_id', $orderId)
            ->groupBy('title')
            ->select(
                DB::raw("CONCAT(pc.name, ' - ', p.title) as title"),
                'pu.short_name',
                DB::raw('SUM(oi.ordered_quantity) as orderquantity'),
                'oi.price',
                // 'oi.quantity as receivedquantity',
                DB::raw('SUM(oi.quantity) as receivedquantity'),
                DB::raw('SUM(oi.ordered_quantity * oi.price) as total_price')
            )
            ->orderBy('title')
            ->get();
        $order = Order::query()
            ->where('id', '=', $orderId)
            ->select('total_tax','total','sub_total', 'grn_invoice', 'sales_note')
            ->get();
        return ['data' => $productDetailsQuery, 'order' => $order];
    }

    public static function getOrderItemssummaryDetails($orderId)
   {
        $productDetailsQuery = DB::table('order_items as oi')
            ->join('products as p', 'oi.product_id', '=', 'p.id')
            ->join('product_variants as pv','pv.id', '=', 'oi.variant_id')
            ->join('product_categories as pc', 'p.category_id', '=', 'pc.id')
            ->join('product_units as pu','p.unit_id', '=', 'pu.id')
            ->where('oi.order_id', $orderId)
            ->groupBy('title')
            ->select(
                DB::raw("CONCAT(pc.name, ' - ', p.title) as title"),
                'pu.short_name',
                DB::raw('SUM(oi.ordered_quantity) as orderquantity'),
                'oi.price',
                // 'oi.quantity as receivedquantity',
                DB::raw('SUM(oi.quantity) as receivedquantity'),
                DB::raw('SUM(oi.ordered_quantity * oi.price) as total_price'),
                'pc.name as course',
                'p.title as item',
                'pv.variant_title as item_size'

            )
            ->orderBy('title')
            ->get();
        
        return $productDetailsQuery;
    }

    public function displaywords($number)
    {
        $words = array(
            '0' => '', '1' => 'one', '2' => 'two', '3' => 'three', '4' => 'four', '5' => 'five', '6' => 'six',
            '7' => 'seven', '8' => 'eight', '9' => 'nine', '10' => 'ten', '11' => 'eleven', '12' => 'twelve',
            '13' => 'thirteen', '14' => 'fourteen', '15' => 'fifteen', '16' => 'sixteen', '17' => 'seventeen',
            '18' => 'eighteen', '19' =>'nineteen', '20' => 'twenty', '30' => 'thirty', '40' => 'forty',
            '50' => 'fifty', '60' => 'sixty', '70' => 'seventy', '80' => 'eighty', '90' => 'ninety'
        );
        $digits = array('', 'hundred', 'thousand', 'lakh', 'crore');
 
        $number = explode(".", $number);
        $result = array("", "");
        $j = 0;
 
        foreach($number as $val){
            $val = str_pad($val, strlen($val) + (strlen($val) % 2), "0", STR_PAD_LEFT); // Ensure even length for proper grouping
            $len = strlen($val);
            $numberpart = '';
            for($i = 0; $i < $len; $i += 2){
                $num = substr($val, $i, 2);
                if($num > 0){
                    if($num < 21){
                        $numberpart .= $words[$num] . " ";
                    }else{
                        $numberpart .= $words[$num[0] * 10] . " " . $words[$num[1]] . " ";
                    }
                    if($len > $i + 2){
                        $numberpart .= $digits[($len - $i - 2) / 2] . " ";
                    }
                }
            }
            $result[$j] .= $numberpart;
            $j++;
        }
 
        $output = '';
        if(trim($result[0]) != "") $output .= trim($result[0]) . "Rupees ";
        if(trim($result[1]) != "") $output .= trim($result[1]) . "Paise";
        $output .= " Only";
 
        return $output;
    }
 

    public function yearlySalesChart(Request $request)
    {
        $salesFilterData = $request->filterData;
        $year = date("Y");
        $currentMonth = date("m");

        if (!isset($salesFilterData['duration'])) {
            $salesFilterData['duration'] = 'this_year';
        }

        $duration = $salesFilterData['duration'];

        $salesChart = Payments::yearFilter($salesFilterData, $year, $currentMonth);

        if ($duration == 'last_month' || $duration == 'this_month') {

            if ($duration == 'this_month') {
                $days = date("t");
            } else {
                $days = date("t", mktime(0, 0, 0, date("n") - 1));
            }

            return ['salesChartData' => $salesChart, "salesFilterData" => $salesFilterData, "days" => $days];
        } else {
            $salesChartData = array(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

            foreach ($salesChart as $data) {
                $salesChartData[$data->month - 1] = $data->sales;
            }
        }

        return ['salesChartData' => $salesChartData, "salesFilterData" => $salesFilterData];
    }

    public function availableStockChart()
    {
        return Product::availableStock();
    }

    public function getBranchAndUser()
    {
        $user = CustomUser::getAll('*', 'user_type', 'staff');
        $branch = Branch::allData();

        return ['user' => $user, 'branch' => $branch];
    }

    public function taxReports(Request $request)
    {
        // Export functionality
        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new TaxReportExport(), 'TaxReport.xlsx');
        }

        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;

        $tax = Order::taxReports($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);

        if (empty($requestType)) {

            $taxData = $tax['data'];
            $totalCount = $tax['count'];

            $taxItems = $this->calculateTax($taxData);
            $arrayCount = $taxItems['count'];

            $taxData[$arrayCount] = ['invoice_id' => Lang::get('lang.total'), 'total' => $taxItems['netTotal'], 'tax' => $taxItems['netTax']];

            $grandCalculation = $this->calculateTax($tax['allData']);

            $taxData[$arrayCount + 1] = ['invoice_id' => Lang::get('lang.grand_total'), 'total' => $grandCalculation['netTotal'], 'tax' => $grandCalculation['netTax']];

            $tax['allData'][] = ['invoice_id' => Lang::get('lang.grand_total'), 'total' => $grandCalculation['netTotal'], 'tax' => $grandCalculation['netTax']];

            return ['datarows' => $taxData, 'count' => $totalCount, 'all_data' => $tax['allData']];
        } else {
            $this->calculateTax($tax);
            return ['datarows' => $tax];
        }
    }

    public function calculateTax($data)
    {
        $netTotal = 0;
        $netTax = 0;
        $arrayCount = 0;
        foreach ($data as $rowData) {
            $netTax += $rowData->tax;
            $netTotal += $rowData->total;
            $arrayCount++;

            if ($rowData->order_type == 'sales') {
                $rowData->order_type = Lang::get('lang.sales');
            } else {
                $rowData->order_type = Lang::get('lang.receiving');
            }
        }

        return ['netTotal' => $netTotal, 'netTax' => $netTax, 'count' => $arrayCount];
    }

    public function profitLossReport(Request $request)
    {
         // Export functionality
         if ($request->has('export') && $request->export == true) {
            return \Excel::download(new ProfitLossReportExport(), 'ProfitLossReport.xlsx');
        }

        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;
        $profitFilter = null;
        $profit = Order::getProfit($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        if (empty($requestType)) {
            $profitData = $profit['data'];
            $profitFilter = $profit['filter'];
        } else {
            $profitData = $profit;
        }
        if (empty($requestType)) {
            $totalCount = $profit['count'];

            $totalProfit = $this->calculateProfit($profitData, $profitFilter);
            $profitData = $totalProfit['data'];
            $rowCount = $totalProfit['count'];
            $profitData[$rowCount] = [$profitFilter => Lang::get('lang.total'), "grand_total" => $totalProfit['netTotal'], 'item_tax' => $totalProfit['netTax'], 'profit_amount' => $totalProfit['netProfit']];

            $grandProfit = $this->calculateProfit($profit['allData'], null);
            $profitData[$rowCount + 1] = [$profitFilter => Lang::get('lang.grand_total'), "grand_total" => $grandProfit['netTotal'], 'item_tax' => $grandProfit['netTax'], 'profit_amount' => $grandProfit['netProfit']];
           
            $profit['allData'][] = [$profitFilter => Lang::get('lang.grand_total'), "grand_total" => $grandProfit['netTotal'], 'item_tax' => $grandProfit['netTax'], 'profit_amount' => $grandProfit['netProfit']];
            return ['datarows' => $profitData, 'count' => $totalCount, 'all_data' => $profit['allData']];
        } else {
            return ['datarows' => $profitData];
        }
    }

    public function calculateProfit($data, $filter)
    {
        $netTotal = 0;
        $netTax = 0;
        $netProfit = 0;
        $arrayCount = 0;
        foreach ($data as $rowData) {
            if ($filter != null) {
                if ($filter == 'customer' && $rowData->customer == null) $rowData->customer = Lang::get('lang.walk_in_customer');
                else if ($filter == 'month') $rowData->month = $this->getMonthName($rowData->month);
            }
            $netTax += $rowData->item_tax;
            $netTotal += $rowData->grand_total;
            $netProfit += $rowData->profit_amount;
            $arrayCount++;
        }
        return ['netTotal' => $netTotal, 'netTax' => $netTax, 'netProfit' => $netProfit, 'count' => $arrayCount, 'data' => $data];
    }

    function getMonthName($monthNumber)
    {
        return date("F", mktime(0, 0, 0, $monthNumber, 1));
    }

    public function getSalesReportFilterData()
    {
        $brands = ProductBrand::index(['name as text', 'id as value']);
        $categories = ProductCategory::index(['name as text', 'id as value']);
        $groups = ProductGroup::index(['name as text', 'id as value']);
        // $customers = Customer::query()->select([DB::raw('concat(first_name," ",last_name) as text'), 'id as value'])->get();
        $employee = CustomUser::getAll([DB::raw('concat(first_name," ",last_name) as text'), 'id as value'], 'user_type', 'staff');
        $customers = [];

        $usersPermissions = Role::select('permissions','id')->get()->toArray();
        $salesRoles = [];
        if(!empty($usersPermissions)){
            foreach($usersPermissions as $key => $rec){                
                $usersPermission = $rec['permissions'];
                $usersPermission = unserialize($usersPermission);
                if (in_array('can_see_sales_person_sales_reports', $usersPermission)) {
                    $salesRoles[] = $rec['id'];
                } 
            }
        }
        $sales_employee = [];
        // if(!empty($salesRoles)){
        //     $sales_employee = CustomUser::salesUserList($salesRoles);
        // } 
        $perm       = new PermissionController();
        $permissions = $perm->checkSalesPermission();

        $paymentMethod  = PaymentType::index(['name as text', 'id as value']);

        return [ 'payment_methods' => $paymentMethod, 'brands' => $brands, 'categories' => $categories, 'groups' => $groups, 'customers' => $customers, 'employee' => $employee, 'permission' => $permissions, 'sales_employee' => $sales_employee];
    }

    public function purchaseReportFilter()
    {

        $suppliers = Supplier::index([DB::raw("CONCAT(suppliers.first_name,' ',suppliers.last_name, ' \n ',suppliers.company)  AS text"), 'id as value']);

        return ['suppliers' => $suppliers];
    }

    /* Sales Details Filter Start */
    public function getSalesDetailsFilterData()
    {
        $brands = ProductBrand::index(['name as text', 'id as value']);
        $categories = ProductCategory::index(['name as text', 'id as value']);
        $groups = ProductGroup::index(['name as text', 'id as value']);
        $allInvoiceId = Order::getAllInvoiceId();

        return ['brands' => $brands, 'categories' => $categories, 'groups' => $groups, 'allInvoiceId' => $allInvoiceId];
    }

    public function getCustomerDueFilterData()
    {
        $customers = Customer::query()->select([DB::raw('concat(first_name," ",last_name) as text'), 'id as value'])->get();
        return ['customers' => $customers];
    }

    public function getCustomerReportFilterData()
    {
        $customerGroup = CustomerGroup::index(['title as text', 'id as value']);
        $branchList = Branch::index(['name as text', 'id as value']);

        return ['customerGroup' => $customerGroup, 'branchList' => $branchList];
    }

    public function calculateCustomerSummary($salesData)
    {
        $netTotalPayment = 0;
        $netTotalReturn = 0;
        $netTotalSales = 0;
        $netDueAmount = 0;
        $arrayCount = 0;
        foreach ($salesData as $rowData) {
            if ($rowData->name == null) $rowData->name = Lang::get('lang.walk_in_customer');
            $netTotalPayment += $rowData->total_payment;
            $netTotalReturn += $rowData->total_return;
            $netTotalSales += $rowData->total_sales;
            $netDueAmount += $rowData->due;
            $arrayCount++;
        }
        return [
            'netTotalPayment' => $netTotalPayment,
            'netTotalReturn' => $netTotalReturn,
            'netTotalSales' => $netTotalSales,
            'netDueAmount' => $netDueAmount,
            'count' => $arrayCount
        ];
    }

    public function customerSummaryReport(Request $request)
    {
        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new CustomersSummaryReportExport(), 'CustomersSummaryReport.xlsx');
        }

        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $requestType = $request->requestType;
        $searchValue = searchHelper::inputSearch($request->searchValue);

        $customerReport = Order::customerReport($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        $customerData = [];
        if (empty($requestType)) {
            $customerData = $customerReport['data'];
        } else {
            $customerData = $customerReport;
        }

        if (empty($requestType)) {
            $customerDataSum = $this->calculateCustomerSummary($customerData);

            $arrayCount = $customerDataSum['count'];
            $totalCount = count($customerReport['allData']);
            $customerData[$arrayCount] = [
                'name' => Lang::get('lang.total'),
                'total_payment' => $customerDataSum['netTotalPayment'],
                'total_return' => $customerDataSum['netTotalReturn'],
                'total_sales' => $customerDataSum['netTotalSales'],
                'due' => $customerDataSum['netDueAmount'],
            ];

            $grandCalculation = $this->calculateCustomerSummary($customerReport['allData']);

            $customerData[$arrayCount + 1] = [
                'name' => Lang::get('lang.grand_total'),
                'total_payment' => $grandCalculation['netTotalPayment'],
                'total_return' => $grandCalculation['netTotalReturn'],
                'total_sales' => $grandCalculation['netTotalSales'],
                'due' => $grandCalculation['netDueAmount'],
            ];

            $customerReport['allData'][] = [
                'name' => Lang::get('lang.grand_total'),
                'total_payment' => $grandCalculation['netTotalPayment'],
                'total_return' => $grandCalculation['netTotalReturn'],
                'total_sales' => $grandCalculation['netTotalSales'],
                'due' => $grandCalculation['netDueAmount'],
            ];

            return ['datarows' => $customerData, 'count' => $totalCount, 'all_data' => $customerReport['allData']];
        } else {
            $this->calculateCustomerSummary($customerData);

            return ['datarows' => $customerData];
        }
    }

    public function supplierSummaryReport(Request $request)
    {
        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new SuppliersSummaryReportExport(), 'SuppliersSummaryReport.xlsx');
        }
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $requestType = $request->requestType;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        // \DB::enableQueryLog();
        $supplierReport = Order::supplierReport($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        // dd(\DB::getQueryLog());
        $supplierData = [];

        if (empty($requestType)) $supplierData = $supplierReport['data'];
        else $supplierData = $supplierReport;

        if (empty($requestType)) {
            $supplierDataSum = $this->calculateSupplierSummary($supplierData);
            $arrayCount = $supplierDataSum['count'];
            $totalCount = count($supplierReport['allData']);
            $supplierData[$arrayCount] = [
                'name' => Lang::get('lang.total'),
                'total_payment' => $supplierDataSum['netTotalPayment'],
                'total_purchase' => $supplierDataSum['netTotalPurchase'],
                'due' => $supplierDataSum['netDueAmount'],
            ];
            $grandCalculation = $this->calculateSupplierSummary($supplierReport['allData']);

            $supplierData[$arrayCount + 1] = [
                'name' => Lang::get('lang.grand_total'),
                'total_payment' => $grandCalculation['netTotalPayment'],
                'total_purchase' => $grandCalculation['netTotalPurchase'],
                'due' => $grandCalculation['netDueAmount'],
            ];

            $supplierReport['allData'][] = [
                'name' => Lang::get('lang.grand_total'),
                'total_payment' => $grandCalculation['netTotalPayment'],
                'total_purchase' => $grandCalculation['netTotalPurchase'],
                'due' => $grandCalculation['netDueAmount'],
            ];

            return ['datarows' => $supplierData, 'count' => $totalCount, 'all_data' => $supplierReport['allData']];
        } else {
            $this->calculateSupplierSummary($supplierData);
            return ['datarows' => $supplierData];
        }
    }

    public function calculateSupplierSummary($salesData)
    {
        $netTotalPayment = 0;
        $netTotalPurchase = 0;
        $netDueAmount = 0;
        $arrayCount = 0;
        foreach ($salesData as $rowData) {
            if ($rowData->name == null) $rowData->name = Lang::get('lang.walk_in_supplier');
            $netTotalPayment += $rowData->total_payment;
            $netTotalPurchase += $rowData->total_purchase;
            $netDueAmount += $rowData->due;
            $arrayCount++;
        }
        return [
            'netTotalPayment' => $netTotalPayment,
            'netTotalPurchase' => $netTotalPurchase,
            'netDueAmount' => $netDueAmount,
            'count' => $arrayCount
        ];
    }

    public function salesAndPurchaseReport(Request $request)
    {
        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new SalesPurchasesExport(), 'SalesPurchasesReport.xlsx');
        }
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $requestType = $request->requestType;

        $totalSales = Order::totalSalesAmount($filtersData, $request->searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        return ['datarows' => $totalSales['data'], 'all_data' => $totalSales['data']];
    }

    public function adjustStockReport(Request $request)
    {
        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new StockAdjustmentExport(), 'StockAdjustmentReport.xlsx');
        }
        
        if ($request->rowLimit) $limit = $request->rowLimit;
        if ($request->columnKey) $columnName = $request->columnKey;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;
        $columnSortedBy = $request->columnSortedBy;
         
        
        $rowOffset = $request->rowOffset;
// \DB::enableQueryLog();
        $adjustmentItems = OrderItems::adjustmentItems($filtersData, $searchValue, $columnSortedBy, $limit, $rowOffset, $columnName, $requestType);
// dd(\DB::getQueryLog());
        if (empty($requestType)) {
            $totalCount = count($adjustmentItems['allData']);
            $adjustmentItemData = $this->calculateAdjustStock($adjustmentItems['data']);
            return ['datarows' => $adjustmentItemData['data'], 'count' => $totalCount, 'all_data' => $adjustmentItems['allData']];
        } else {
            $adjustmentItemData = $this->calculateAdjustStock($adjustmentItems);
            return ['datarows' => $adjustmentItemData['data']];
        }
    }

    public function calculateAdjustStock($data)
    {
        $arrayCount = 0;
        foreach ($data as $rowData) {
            if ($rowData->variant_title == 'default_variant') $rowData->variant_title = Lang::get('lang.default_variant');
            $arrayCount++;
        }
        return [
            'count' => $arrayCount,
            'data' => $data
        ];
    }

    public function getAdjustmentReportFilterData()
    {
        $branches = Branch::index(['name as text', 'id as value']);
        $products = Product::index(['title as text', 'id as value']);
        $adjustmentTypes = AdjustProductStockType::index(['title as text', 'id as value']);
        $brandName = ProductBrand::index(['name as text', 'id as value']);
        $categoryName = ProductCategory::index(['name as text', 'id as value']);
        $groupName = ProductGroup::index(['name as text', 'id as value']);
        return ['branches' => $branches, 'products' => $products, 'adjustmentTypes' => $adjustmentTypes,  'brandName' => $brandName, 'categoryName' => $categoryName, 'groupName' => $groupName];
    }

    public function shipmentReport(Request $request)
    {
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;

        $shipments = ShippingInformation::shipmentReports($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);

        foreach ($shipments['data'] as $shipment) {
            if ($shipment->status == 'pending') {
                $shipment->status = Lang::get('lang.pending');
            } else if ($shipment->status == 'packet') {
                $shipment->status = Lang::get('lang.packet');
            } else if ($shipment->status == 'on the way') {
                $shipment->status = Lang::get('lang.on_the_way');
            } else if ($shipment->status == 'delivered') {
                $shipment->status = Lang::get('lang.delivered');
            } else {
                $shipment->status = Lang::get('lang.cancelled');
            }
        }

        return ['datarows' => $shipments['data'], 'count' => $shipments['count']];
    }

    public function getPaymentRecord(Request $request, $id)
    {
        $payments = Payments::getPaymentTypeInfo($id);
        return $payments;
    }    
    public static function saveGrnRecordData(Request $request, $id)
    {

        $invoice_no     = trim(addslashes($request->grn_invoice_number_ref));
        $timezone       = Config::get('app.timezone'); 
        $date           = Carbon::parse($request->grn_invoice_ref_date)
                        ->setTimezone($timezone)
                        ->format('Y-m-d');
        if($invoice_no != ""){
            $update         = Order::where('id', '=', $id)
                            ->update(['grn_invoice_number_ref' => $invoice_no, 'grn_invoice_ref_date' => $date ]); 
        }                        
        
    
            $response = [
                'message' => Lang::get('lang.grn') . ' ' . Lang::get('lang.successfully_updated')
            ];

            return response()->json($response, 200);
         
    }
    public function savePaymentRecord(Request $request, $id)
    {
        $paymentMethod  = $request->payment_method;
        $payments       = Payments::updatePaymentTypeInfo($id,$paymentMethod);
        return ['data' => 'Payment method updated successfully'];

    }
    public function getPurchaseRecord(Request $request, $id)
    {
        $payments = Order::getOrderInfoForPurchase($id);
        return $payments;
    }
    public function savePurchaseRecord(Request $request, $id)
    {
        $type       = $request->type;        
        $paid       = $request->paid;
        $due        = $request->due;
        $sid        = $request->SupplierID;
        $purchaseRecord = Order::getOrderInfoForPurchase($id)->first();
        $due_amount = (int) $purchaseRecord->due_amount;
        $total      = (int) $purchaseRecord->total;
        $supplier_orders_id = $purchaseRecord->supplier_orders_id;
        $supplierID   =  $purchaseRecord->supplier_id;
        $error      = false;
        $response   = '';

        if($total < $due ){
            $error      = true;
            $response   = 'Due amount should not be exceed total purchase amount';
        }
        
        if(!$error){
            $data           =   [   
                                    'id'    => $id, 
                                    'total' => $total,
                                    'due'   => $due,
                                    'paid'  => $paid,
                                    'type'  => $type,
                                    'supplier_orders_id' => $supplier_orders_id
                                ];
            $purchaseRecord = Order::updatePurchaseOrder($data);

            \DB::table('supplier_orders')
            ->where("id", $supplier_orders_id)->where("order_id_fk", $id)->update(["payment_status" => $type,"payment_amount" => $paid]);

            if($supplierID == null ){
                \DB::table('orders')->where("id", $id)->update(["supplier_id" => $sid]);
                \DB::table('supplier_orders')->insert([
                    'order_id_fk'       => $id,
                    'supplier_id_fk'    => $sid,
                    'payment_status'    => $type,//1-paid,2-due,0-none
                    'payment_amount'    => $paid,
                    'created_at'        => date('Y-m-d H:i:s')
                ]);
            }

            $response       = 'Purchase updated successfully';
        }
        return [ 'error' => $error, 'data' => $response];
    }  

    public function getCustomerComboReportFilterData(){
        $products = Product::index(['title as text', 'id as value']);
        $courses = ProductCourse::index(['course_name as text', 'id as value']);
        $brandName = ProductBrand::index(['name as text', 'id as value']);
        $categoryName = ProductCategory::index(['name as text', 'id as value']);
        $groupName = ProductGroup::index(['name as text', 'id as value']);
        

        return ['products' => $products, 'courses' => $courses,  'brandName' => $brandName, 'categoryName' => $categoryName, 'groupName' => $groupName];
    }
    
    public function getCustomerComboReport(Request $request)
    {
        ini_set('memory_limit', '1G');
        
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;

        if ($request->has('export') && $request->export == true) {
            return \Excel::download(new StudentComboReport($filtersData,$searchValue), 'stdent-combo-report.xlsx');
        }

        //  DB::enableQueryLog();
        $customerData = Customer::getCustomerCombo($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        //  dd(DB::getQueryLog()); 
        $__customerData = $customerData['data'];
        $_customerData = [];
        $init_less = 0;
        if(!empty($__customerData)){
            foreach($__customerData as $key => $cust){

                if ($cust->is_jeans == '1' &&  $cust->is_kurti == '1'){
                    $sql  = "SELECT 
                                CASE    WHEN order_items.product_id IN (62, 64) THEN 'jeans' 
                                        WHEN order_items.product_id IN (76, 82) THEN 'kurti' 
                                END AS product_group,
                                SUM(order_items.quantity) AS total_quantity
                            FROM  orders 
                            LEFT JOIN  order_items ON order_items.order_id = orders.id 
                            LEFT JOIN  customers ON customers.id = orders.customer_id AND customers.gender_id = 'F' 
                            WHERE  order_items.product_id IN (62, 64, 76, 82) AND orders.customer_id IS NOT NULL  AND orders.status = 'done' 
                                AND orders.customer_id = $cust->id 
                            GROUP BY  product_group
                            HAVING total_quantity < 0;";
                    $results = DB::select($sql);
                    $get_active_products = isset($results[0]) ? $results[0] : [];                    
                    $products_group = isset($get_active_products->product_group) ? $get_active_products->product_group : '';
                    if($products_group == 'kurti'){
                        $cust->is_jeans = null;
                        $cust->is_kurti = 1;
                    }else{                        
                        $cust->is_jeans = 1;
                        $cust->is_kurti = null;                        
                    }
                }                
                if($cust->is_jeans == '1' && $cust->is_kurti != '1'){
                    if(in_array($cust->product_id, [76,82] ) ){
                        #skip
                        $init_less++;
                    }else{
                        $_customerData[] =$cust;
                    }
                }
                else if($cust->is_kurti == '1' && $cust->is_jeans != '1'){
                    if(in_array($cust->product_id, [62,64] ) ){
                        #skip
                        $init_less++;
                    }else{
                        $_customerData[] =$cust;
                    }
                }
                else if ($cust->is_jeans != '1' &&  $cust->is_kurti != '1'){
                    $_customerData[] =$cust;
                }
            }
        }

        $totalCount = $customerData['count'] - $init_less;       
        return ['datarows' => $_customerData, 'count' => $totalCount    ];         
    }  

    public function stockReport(Request $request)
    {
        $branchIds = explode(',', Auth::user()->branch_id);
 
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;
        $branchIds = explode(',', Auth::user()->branch_id);

        if ($request->has('export') && $request->export == true) {

            return \Excel::download(new StockReport($filtersData, $searchValue, $branchIds), 'stock-report.xlsx');

            // return \Excel::download(new StockReport($filtersData,$searchValue), 'stock-report.xlsx');
        }

        //  DB::enableQueryLog();
        $productData = Product::getProductStockReport($filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        //  dd(DB::getQueryLog());

        $_productData = [];
        $__productData = $productData['data'];

        

        if(!empty($__productData)){
            foreach($__productData as $key => $rec){

                $summary = Product::getStockSummary($rec['product_id'], $rec['variant_id'], $filtersData, $branchIds);

                $rec['opening_qty']   = $summary['opening_qty'];
                $rec['purchase_qty']  = $summary['purchase_qty'];
                $rec['sale_qty']      = $summary['sale_qty'];
                $rec['inventory_qty'] = $summary['inventory_qty'];

                $_productData[] = $rec;


                // #openingData
                // $productQry = DB::table('products')
                // ->select(DB::raw('SUM(order_items.quantity) as opening_qty'), 'orders.date')
                // ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
                // ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
                // ->where('products.id', $rec['product_id'])
                // ->where('order_items.variant_id', $rec['variant_id'])
                // ->where('orders.status', 'done')
                // ->whereIn('orders.branch_id', $branchIds);  
                   
                // if (!empty($filtersData)) {
                //     foreach ($filtersData as $singleFilter) {
                //         if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                //             $starts = $singleFilter['value'][0]['start'];
                //             $ends = $singleFilter['value'][0]['end']; 
                //             $productQry->where('orders.date', '<', $starts);
                //         } 
                //     }   
                // } 
                // $openingData = $productQry->groupBy('order_items.variant_id')->first(); 

                // #purchaseData
                // $productQry2purchase = DB::table('products')
                // ->select(DB::raw('SUM(order_items.quantity) as purchase_qty'), 'orders.date')
                // ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
                // ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
                // ->where('products.id', $rec['product_id'])
                // ->where('order_items.variant_id', $rec['variant_id'])
                // ->where('orders.status', 'done')  
                // ->where('orders.order_type', '!=','sales')
                // ->whereIn('orders.branch_id', $branchIds);     
                // if (!empty($filtersData)) {
                //     foreach ($filtersData as $singleFilter) {
                //         if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                //             $starts = $singleFilter['value'][0]['start'];
                //             $ends = $singleFilter['value'][0]['end']; 
                //             $productQry2purchase->whereBetween('orders.date', [$starts, $ends]);
                //         } 
                //     }   
                // } 
                // $purchaseData = $productQry2purchase->groupBy('order_items.variant_id')->first(); 

                // #saleData inventory_qty
                // $productQry2Sales = DB::table('products')
                // ->select(DB::raw('-SUM(order_items.quantity) as sale_qty'), 'orders.date')
                // ->leftJoin('order_items', 'order_items.product_id', '=', 'products.id')
                // ->leftJoin('orders', 'orders.id', '=', 'order_items.order_id')
                // ->where('products.id', $rec['product_id'])
                // ->where('order_items.variant_id', $rec['variant_id'])
                // ->where('orders.status', 'done')  
                // ->where('orders.order_type', 'sales')
                // ->whereIn('orders.branch_id', $branchIds);     
                // if (!empty($filtersData)) {
                //     foreach ($filtersData as $singleFilter) {
                //         if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                //             $starts = $singleFilter['value'][0]['start'];
                //             $ends = $singleFilter['value'][0]['end']; 
                //             $productQry2Sales->whereBetween('orders.date', [$starts, $ends]);
                //         } 
                //     }   
                // } 
                // $salesData = $productQry2Sales->groupBy('order_items.variant_id')->first(); 
              

                // $rec['opening_qty']     = isset($openingData->opening_qty) ? $openingData->opening_qty : 0;
                // $rec['purchase_qty']    = isset($purchaseData->purchase_qty) ? $purchaseData->purchase_qty : 0;
                // $rec['sale_qty']        = isset($salesData->sale_qty) ? $salesData->sale_qty : 0;
                
                
                // $rec['inventory_qty']   = ($rec['opening_qty'] + $rec['purchase_qty'] ) - $rec['sale_qty'];
                // $_productData[]         = $rec;
            }
        }

        $totalCount = $productData['count'];       
        return ['datarows' => $_productData, 'count' => $totalCount];
    }
}
