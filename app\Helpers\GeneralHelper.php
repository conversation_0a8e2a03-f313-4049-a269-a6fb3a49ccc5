<?php
 
function currencyToWords($amount, $currency = 'USD') {
    // Arrays for converting numbers to words
    $units = ['', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight', 'nine'];
    $teens = ['ten', 'eleven', 'twelve', 'thirteen', 'fourteen', 'fifteen', 'sixteen', 'seventeen', 'eighteen', 'nineteen'];
    $tens = ['', 'ten', 'twenty', 'thirty', 'forty', 'fifty', 'sixty', 'seventy', 'eighty', 'ninety'];
    $thousands = ['', 'thousand', 'million', 'billion', 'trillion']; // Extend as needed
    // Split the amount into integer and fractional parts
    $integerPart = (int) $amount;
    $fractionalPart = round(($amount - $integerPart) * 100);
 
    // Convert integer part to words
    $integerWords = [];
    $count = 0;
    do {
        $chunk = $integerPart % 1000;
        if ($chunk != 0) {
            $chunkWords = [];
            if ($chunk >= 100) {
                $chunkWords[] = $units[floor($chunk / 100)] . ' hundred';
                $chunk %= 100;
            }
            if ($chunk >= 20) {
                $chunkWords[] = $tens[floor($chunk / 10)];
                $chunk %= 10;
            }
            if ($chunk > 0) {
                if ($chunk < 10) {
                    $chunkWords[] = $units[$chunk];
                } else {
                    $chunkWords[] = $teens[$chunk - 10];
                }
            }
            $integerWords[] = implode(' ', $chunkWords) . ' ' . $thousands[$count];
        }
        $integerPart = floor($integerPart / 1000);
        $count++;
    } while ($integerPart > 0);
 
    // Combine integer words and handle fractional part
    $words = implode(' ', array_reverse($integerWords));
     // Append currency name if needed
     switch ($currency) {
        case 'USD':
            $words .= ' dollars';
            break;
        case 'INR':
            $words .= ' rupees';
            break;
        // Add more currencies as needed
        default:
            $words .= ' ' . $currency;
            break;
    }
    if ($fractionalPart > 0) {
   
        $fractionalWords = $tens[floor($fractionalPart / 10)] . ' ' . $units[$fractionalPart % 10];
        $words .= ' and ' . $fractionalWords . ' paise';
    }
 
    
 
    return ucfirst(trim($words));
}