<?php

namespace App\Http\Controllers\API;

use App\Libraries\Permissions;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Validator;
use App\Models\CommonModel;
use App\Models\Supplier;
use App\Models\Setting;
use App\Libraries\imageHandler;
use App\Exports\ExportData;
use Maatwebsite\Excel\Facades\Excel;
use DB;

class InventoryController extends Controller
{
    public function permissionCheck()
    {
        return new Permissions;
    }

    public function unitsIndex(Request $request)
    {
        $limit = 0;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $offset = $request->rowOffset;
        $req_param = array(
            "fields"    => array('*'),
            "table"     => 'inventory_units_list',
        );
        
        if(isset($request->searchValue)) {
            $search_columns_arr = ['unit_name','short_name'];
            $req_param['search'] = array(
                "search_columns_arr" => $search_columns_arr,
                "search_string" => $request->searchValue
            );
        }

        if($limit != 0) $req_param = array_merge($req_param, array("limit" => array('offset' => $offset,'limit' => $limit)));
        $list = CommonModel::get_data($req_param);
        return $list;
    }

    public function unitsStore(Request $request)
    {
        $validator = Validator::make($request->all(), [ 
            "unit_name" => "required",
            "short_name"=> "required",
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }

        $array = array(
            "table"             => 'inventory_units_list',
            "fields"            => array('*'),
            "where"             => array("unit_name" => $request->unit_name),
        );
        if(CommonModel::get_row($array)){
            $response = [
                'message' => Lang::get('lang.unit') . ' already exists!'
            ];
            return response()->json($response, 404);
        }

        $array = array(
            "table"             => 'inventory_units_list',
            "fields"            => array('*'),
            "where"           => array("short_name" => $request->short_name)
        );
        if(CommonModel::get_row($array)){
            $response = [
                'message' => Lang::get('lang.short_name') . ' already exists!'
            ];
            return response()->json($response, 404);
        }
        
        $array = array(
            "table"    => "inventory_units_list",
            "data"     => array('unit_name' => $request->unit_name,"short_name" => $request->short_name,"created_by" => Auth::user()->id)
        );
        
        if(CommonModel::insert_data($array)) {
            $response = [
                'message' => Lang::get('lang.unit') . ' ' . Lang::get('lang.successfully_saved')
            ];
            return response()->json($response, 201);
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];

        return response()->json($response, 404);
    }

    public function getUnitData($id)
    {
        $array = array(
            "table"             => 'inventory_units_list',
            "fields"            => array('*'),
            "where"             => array("id" => $id)
        );
        return CommonModel::get_row($array);
    }

    public function getUnitUpdate(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [ 
            "unit_name"  => "required",
            "short_name" => "required"
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }

        $array = array(
            "table"             => 'inventory_units_list',
            "fields"            => array('id'),
            "where"             => array("short_name" => $request->short_name),
        );
        $row_id = CommonModel::get_row($array);
        
        if(isset($row_id) && $id != $row_id->id){
            $response = [
                'message' => Lang::get('lang.short_name') . ' already exists!'
            ];
            return response()->json($response, 201);
        }

        $array = array(
            "table"    => "inventory_units_list",
            "data"     => array('unit_name' => $request->unit_name,"short_name" => $request->short_name,'updated_at' => date('Y-m-d H:i:s')),
            "where"    => array("id" => $id),
        );
        
        if(CommonModel::update_data($array)) {
            $response = [
                'message' => Lang::get('lang.unit') . ' ' . Lang::get('lang.successfully_updated')
            ];
            return response()->json($response, 201);
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];

        return response()->json($response, 404);
    }

    public function unitDelete($id)
    {
        $array = array(
            "table"             => 'inventory_items_list',
            "fields"            => array('*'),
            "where"             => array("unit_id_fk" => $id),
        );
        if(CommonModel::get_row($array)){
            $response = [
                'message' => Lang::get('lang.unit').' '.Lang::get('lang.in_use').', '.Lang::get('lang.you_can_not_delete_the').' '.strtolower(Lang::get('lang.unit'))
            ];
            return response()->json($response, 201);
        } else {      
            $array = array(
                "table"             => "inventory_units_list",
                "where"             => array("id" => $id),
            );
            if(CommonModel::delete_data($array)) {
                $response = [
                    'message' => Lang::get('lang.unit') . ' ' . Lang::get('lang.successfully_deleted')
                ];
                return response()->json($response, 200);
            }
        }
        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];
        return response()->json($response, 404);
    }

    public function itemsIndex(Request $request)
    {
        $limit = 0;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $offset = $request->rowOffset;
        $req_param = array(
            "fields"    => array('item.*','unit.id as u_id','unit.unit_name','unit.short_name'),
            "table"     => 'inventory_items_list as item',
            "left_join"         => [['table' => 'inventory_units_list as unit', 'left_side' => 'unit.id', 'right_side' => 'item.unit_id_fk']],
        );
        if(isset($request->searchValue)) {
            $search_columns_arr = ['item.item_name'];
            $req_param['search'] = array(
                "search_columns_arr" => $search_columns_arr,
                "search_string" => $request->searchValue
            );
        }
        if($limit != 0) $req_param = array_merge($req_param, array("limit" => array('offset' => $offset,'limit' => $limit)));
        $list = CommonModel::get_data($req_param);
        return $list;
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [ 
            "item_name" => "required",
            "unit"      => "required"
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }

        $array = array(
            "table"             => 'inventory_items_list',
            "fields"            => array('*'),
            "where"             => array("item_name" => $request->item_name),
        );
        if(CommonModel::get_row($array)){
            $response = [
                'message' => Lang::get('lang.item') . ' already exists!'
            ];
            return response()->json($response, 201);
        }
        
        $array = array(
            "table"    => "inventory_items_list",
            "data"     => array('item_name' => $request->item_name, "unit_id_fk" => $request->unit, "created_by" => Auth::user()->id)
        );
        
        $id = CommonModel::insert_data_id($array);
        if($id) {
            if($request->open_stock != '') {
                $prefix = Setting::getSettingValue('inventory_prefix')->setting_value;
                $lastInvoiceNum = Setting::getSettingValue('last_inventory_number')->setting_value;
                $array = array("supplier_id_fk" => 1, "date" => date('Y-m-d'),"inventory_type" => 'inventory', "type" => 'receiving',"created_by" => Auth::user()->id,"invoice_id" => $prefix.''.$lastInvoiceNum);
                $data = array(
                    "table"    => "inventory_invoice",
                    "data"     => $array
                );
                $insert = CommonModel::insert_data_id($data);
                $lastInvoiceNum += 1;
                $lastUpdatedInvoice = Setting::getSettingValue('last_inventory_number')->setting_value;
                if ($lastInvoiceNum > $lastUpdatedInvoice) {
                    Setting::updateSetting('last_inventory_number', $lastInvoiceNum);
                }

                $array = array(
                            'invoice_id_fk'     => $insert,
                            'item_id_fk'        => $id,
                            "inventory_type"    => 'inventory',
                            "type"              => 'receiving',
                            "quantity"          => $request->open_stock,                    
                            "created_by"        => Auth::user()->id
                         );
                $array = array(
                    "table"    => "inventory_invoice_items",
                    "data"     => $array
                );
                CommonModel::insert_data($array);
            }
            $response = [
                'message' => Lang::get('lang.item') . ' ' . Lang::get('lang.successfully_saved')
            ];
            return response()->json($response, 201);
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];

        return response()->json($response, 404);
    }

    public function itemsImport(Request $request)
    {
        $validator = Validator::make($request->all(), [ 
            "importData" => "required",
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }
        $itemRecords = $this->itemsIndex($request);
        $result = $itemRecords['datarows']->toArray();
        $itemMapping = array_column($result, null, 'item_name');


        $unitRecords = $this->unitsIndex($request);
        $result = $unitRecords['datarows']->toArray();
        $unitMapping = array_column($result, null, 'unit_name');
        // $shortMapping = array_column($result, null, 'short_name');
        foreach ($request->importData as $unit) {
            $record = (object) $unit;

            if (!isset($unitMapping[$record->UNIT_NAME])) {
                // return $record->UNIT_NAME.' => '.$record->UNIT_SHORT_NAME;
                $array = array(
                    "table"    => "inventory_units_list",
                    "data"     => ['unit_name' => $record->UNIT_NAME,"short_name" => $record->UNIT_SHORT_NAME,"created_by" => Auth::user()->id]
                );        
                $insert = CommonModel::insert_data($array);
                $manuallyUnit = [
                    'id'            => $insert,
                    'unit_name'     => $record->UNIT_NAME,
                    'short_name'    => $record->UNIT_SHORT_NAME
                ];
                $unitMapping[$manuallyUnit['unit_name']] = $manuallyUnit;
            }
        }

        // $ItemName = $UnitName = $UnitStName = [];
        $total_count = count($request->importData);
        $insert = $duplicate = 0;
        foreach ($request->importData as $unit) {
            $record = (object) $unit;

            if (!isset($itemMapping[$record->ITEM_NAME])) {
                $id  = (isset($unitMapping[$record->UNIT_NAME])) ? $unitMapping[$record->UNIT_NAME]->id : 0;
                CommonModel::insert_data([
                    "table"    => "inventory_items_list",
                    "data"     => ['item_name' => $record->ITEM_NAME,"unit_id_fk" => $id,"created_by"=>Auth::user()->id]
                ]);
                $insert++;
            } else {
                $duplicate++;
            }
            /*array_push($ItemName, $record->ITEM_NAME);
            array_push($UnitName, $record->UNIT_NAME);
            array_push($UnitStName, $record->UNIT_SHORT_NAME);*/
        }
        return response(['message' => $total_count.' out of '.($insert).' items were imported successfully, while '.($duplicate).' rows were skipped due to duplication.', 'error' => false], 200);
    }

    public function itemsImportOpenStock(Request $request)
    {
        $validator = Validator::make($request->all(), [ 
            "importData" => "required",
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }
        $itemRecords = $this->itemsIndex($request);
        $result = $itemRecords['datarows']->toArray();
        $itemMapping = array_column($result, null, 'item_name');

        $total_count = count($request->importData);
        $prefix = Setting::getSettingValue('inventory_prefix')->setting_value;
        $lastInvoiceNum = Setting::getSettingValue('last_inventory_number')->setting_value;
        
        $array = array(
            "table"    => "inventory_invoice",
            "data"     => ["inventory_type" => "inventory", "supplier_id_fk" => 1,"order_id" => "Open Stock", "date" => date('Y-m-d'), "type" => "receiving","status" => "Completed","created_by" => Auth::user()->id,"invoice_id" => $prefix.''.$lastInvoiceNum]
        );

        $insertId = CommonModel::insert_data_id($array);
        $lastInvoiceNum += 1;
        $lastUpdatedInvoice = Setting::getSettingValue('last_inventory_number')->setting_value;
        if ($lastInvoiceNum > $lastUpdatedInvoice) {
            Setting::updateSetting('last_inventory_number', $lastInvoiceNum);
        }
        $insert = $duplicate = 0;
        foreach ($request->importData as $unit) {
            $record = (object) $unit;

            if (isset($itemMapping[$record->ITEM_NAME])) {
                CommonModel::insert_data([
                    "table"    => "inventory_invoice_items",
                    "data"     => ["invoice_id_fk" => $insertId, "item_id_fk" => $itemMapping[$record->ITEM_NAME]->id, "inventory_type" => "inventory", "type" => "receiving", "quantity" => $record->STOCK_QUANTITY, "price" => $record->UNIT_PRICE, "amount" => $record->COST_PRICE,"created_by"=>Auth::user()->id]
                ]);
                $insert++;
            } else {
                $duplicate++;
            }
        }
        return response(['message' => $total_count.' out of '.($insert).' items stock were imported successfully, while '.($duplicate).' rows were skipped due to items not matching.', 'error' => false], 200);
    }

    public function getItemData($id)
    {
        $array = array(
            "table"             => 'inventory_items_list as item',
            "fields"            => array('item.*','unit.short_name'),
            "where"             => array("item.id" => $id),
            "left_join"         => [['table' => 'inventory_units_list as unit', 'left_side' => 'unit.id', 'right_side' => 'item.unit_id_fk']],
        );
        return CommonModel::get_row($array);
        // return CustomerGroup::getOne($id);
    }

    public function getItemUpdate(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [ 
            "item_name" => "required",
            "unit"      => "required"
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }

        /*$array = array(
            "table"             => 'inventory_items_list',
            "fields"            => array('id'),
            "where"             => array("item_name" => $request->item_name),
        );
        $row_id = CommonModel::get_row($array);
        
        if($id != $row_id->id){
            $response = [
                'message' => Lang::get('lang.item') . ' already exists!'
            ];
            return response()->json($response, 201);
        }*/

        $array = array(
            "table"    => "inventory_items_list",
            "data"     => array('item_name' => $request->item_name,"unit_id_fk" => $request->unit),
            "where"    => array("id" => $id),
        );
        
        if(CommonModel::update_data($array)) {
            $response = [
                'message' => Lang::get('lang.item') . ' ' . Lang::get('lang.successfully_updated')
            ];
            return response()->json($response, 201);
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];

        return response()->json($response, 404);
    }

    public function itemDelete($id)
    {
        $array = array(
            "table"             => 'inventory_invoice_items',
            "fields"            => array('*'),
            "where"             => array("item_id_fk" => $id),
        );
        if(CommonModel::get_row($array)){
            $response = [
                'message' => Lang::get('lang.item').' '.Lang::get('lang.in_use').', '.Lang::get('lang.you_can_not_delete_the').' '.strtolower(Lang::get('lang.item'))
            ];
            return response()->json($response, 201);
        } else {
            $array = array(
                "table"             => "inventory_items_list",
                "where"             => array("id" => $id),
            );
            if(CommonModel::delete_data($array)) {
                $response = [
                    'message' => Lang::get('lang.item') . ' ' . Lang::get('lang.successfully_deleted')
                ];
                return response()->json($response, 200);
            }
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];
        return response()->json($response, 404);
    }

    public function getCustomerGroups()
    {
        $customerGroups = CustomerGroup::customersGroup();
        $states = \DB::table('states')->where('status','1')->select('id', 'state_name as name', 'is_default')->get()->toArray();
        return ['customerGroups' => $customerGroups, 'states' => $states];
    }

    public function getInventoryAllData(Request $request)
    {
        $limit = 0;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $offset = $request->rowOffset;

        $where = $where_bt = array();
        if(isset($request->type)){
            $where['invoice.type'] = $request->type;
            $filtersData = $request->filtersData;
            if(isset($filtersData)) {
                foreach ($filtersData as $singleFilter) {
                    if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                        $where_bt = ["column" => "invoice.date",
                                          "from_date" => $singleFilter['value'][0]['start'],
                                          "to_date" => $singleFilter['value'][0]['end']
                                        ];
                    } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "supplier") {
                        $where['invoice.supplier_id_fk'] = $singleFilter['value'];
                    } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "user") {
                        $where['invoice.user_id_fk'] = $singleFilter['value'];
                    } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "type") {
                        $where['invoice.inventory_type'] = $singleFilter['value'];
                    } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "status") {
                        $where['invoice.status'] = $singleFilter['value'];
                    } else if (array_key_exists('key', $singleFilter)) {
                        $where['invoice.id'] = $singleFilter['value'];
                    }
                }
            }
        }
        if(isset($request->id)) $where["invoice.id"] = $request->id;
        if(isset($request->status)) $where["invoice.status"] = $request->status;

        if($request->checkType == 'download') {
            if(isset($request->supplier)) {
                $where = array_merge($where,["invoice.supplier_id_fk" => $request->supplier]);
            }
            if(isset($request->order_id)) {
                $where = array_merge($where,["invoice.id" => $request->order_id]);
            }
            if(isset($request->invoice_id)) {
                $where = array_merge($where,["invoice.id" => $request->invoice_id]);
            }
            if(isset($request->date_range)) {
                $dt = explode(" - ", $request->date_range);
                $where_bt = [   "column" => "invoice.date",
                                "from_date" => date('Y-m-d', strtotime($dt[0])),
                                "to_date" => date('Y-m-d', strtotime($dt[1]))
                            ];
            }
        }

        $leftJoinArray = []; $fields_raw = '';
        $fields = ['invoice.id as in_id',DB::raw('DATE_FORMAT(invoice.date, "%d/%m/%Y") as date1'),'invoice.date as date',DB::raw('CASE WHEN invoice.inventory_type = "Wastage" THEN "Damage / Expired" ELSE invoice.inventory_type END AS inventory_type'),'invoice.inventory_type as inv_type','invoice.invoice_id','invoice.status']; 
        $fields_raw = 'CONCAT( DATE_FORMAT(invoice.date, "%d/%m/%Y"),  " ",  DATE_FORMAT(invoice.time, "%h:%i"), " ", CASE WHEN DATE_FORMAT(invoice.time, "%p") = "AM" THEN "am" ELSE "pm" END ) AS date_time, ';
        if($request->type == 'receiving') {
            $leftJoinArray[] = ['table' => 'suppliers', 'left_side' => 'suppliers.id', 'right_side' => 'invoice.supplier_id_fk'];
            $fields[] = 'suppliers.id';
            $fields[] = 'invoice.bills';
            $fields[] = 'invoice.order_id';
            $fields_raw .= 'CONCAT(suppliers.first_name," ",suppliers.last_name) AS name';
        } else {
            $leftJoinArray[] = ['table' => 'inventory_sales_users', 'left_side' => 'inventory_sales_users.id', 'right_side' => 'invoice.user_id_fk'];
            $fields[] = 'invoice.time';
            $fields[] = 'invoice.user_id_fk';
            $fields_raw .= 'CONCAT(inventory_sales_users.first_name," ",inventory_sales_users.last_name) AS us_name';
        }

        $req_param = array(
            "fields"        => $fields,
            "fields_raw"    => $fields_raw,
            "table"         => 'inventory_invoice as invoice',
            "left_join"     => $leftJoinArray,
            "where"         => $where,
            "where_bt"      => $where_bt,
            "order_by"      => ["column" => "invoice.id", "direction" => "DESC"]
        );
        if(isset($request->searchValue)) {
            $search_cols_arr = ['invoice.invoice_id','invoice.status'];
            if($request->type == 'receiving') {
                $search_cols_arr[] = 'suppliers.first_name'; $search_cols_arr[] = 'suppliers.last_name';
                $search_cols_arr[] = 'invoice.order_id';
            } else {
                $search_cols_arr[] = 'inventory_sales_users.first_name'; $search_cols_arr[] = 'inventory_sales_users.last_name';
                $search_cols_arr[] = 'invoice.inventory_type';
            }
            // return $search_cols_arr;
            $req_param['search'] = ["search_columns_arr" => $search_cols_arr,"search_string" => $request->searchValue];
        }
        if($limit != 0) $req_param["limit"] = ['offset' => $offset,'limit' => $limit];
        $list = CommonModel::get_data($req_param);

        if($request->checkType == 'download') {
            $excelData = [];$i = 1;
            if($list['datarows'] && count($list['datarows']) > 0){
                foreach ($list['datarows'] as $key => $value) {
                    $excelData[] = array(
                        's_no'      =>  $i,
                        'invoice_id'=>  $value->invoice_id,
                        'date1'     =>  $value->date1,
                        'name'      =>  $value->name,                        
                        'order_id'  =>  $value->order_id,
                    );
                    $i++;
                }
            }
            $headings[] = ["S.No", Lang::get('lang.invoice_id'), Lang::get('lang.date'), Lang::get('lang.supplier_name'),  Lang::get('lang.purchase_order_id')];
            return Excel::download(new ExportData($excelData,$headings), 'purchases.xlsx');
        }

        return $list;
    }

    public function inventoryStore(Request $request)
    {
        $validator = Validator::make($request->all(), [ 
            "supplier"  => $request->inventory_type == 'inventory' ? 'required' : '',
            "date"      => "required",
            "type"      => "required",
            "user"      => $request->inventory_type != 'inventory' ? 'required' : '',
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }
        $request->time  = $request->time == null ? date('H:i') : $request->time;
        
       
        $array = ["inventory_type" => $request->inventory_type, "date" => date('Y-m-d', strtotime($request->date)), "type" => $request->type,"status" => "Draft","created_by" => Auth::user()->id];
        if($request->inventory_type == 'inventory') {
            $prefix = Setting::getSettingValue('inventory_prefix')->setting_value;
            $array["supplier_id_fk"] = $request->supplier;
            $array["order_id"] = $request->order_id;
        } else {
            $prefix = Setting::getSettingValue('transfer_inventory_prefix')->setting_value;
            $array["time"]          = date('H:i:s', strtotime($request->time)); //date('Y-m-d');
            $array["user_id_fk"]    = $request->user;
        }
        $array = array(
            "table"    => "inventory_invoice",
            "data"     => $array
        );

        $insert = CommonModel::insert_data_id($array);
        if($insert) {
            if($request->inventory_type == 'inventory') {
                $lastInvoiceNum = Setting::getSettingValue('last_inventory_number')->setting_value;
            } else {
                $lastInvoiceNum = Setting::getSettingValue('transfer_last_inventory_number')->setting_value;                
            }
            
            $array = array(
                "table"    => "inventory_invoice",
                "data"     => array("invoice_id" => $prefix.''.$lastInvoiceNum),
                "where"    => array("id" => $insert)
            );            
            CommonModel::update_data($array);
            $lastInvoiceNum += 1;
            if($request->inventory_type == 'inventory') {
                $lastUpdatedInvoice = Setting::getSettingValue('last_inventory_number')->setting_value;
                if ($lastInvoiceNum > $lastUpdatedInvoice) {
                    Setting::updateSetting('last_inventory_number', $lastInvoiceNum);
                }
                $response = [
                    'message' => Lang::get('lang.inventory')   . ' ' . Lang::get('lang.successfully_saved'),
                    'id' => $insert
                ];
            } else {
                $lastUpdatedInvoice = Setting::getSettingValue('transfer_last_inventory_number')->setting_value;
                if ($lastInvoiceNum > $lastUpdatedInvoice) {
                    Setting::updateSetting('transfer_last_inventory_number', $lastInvoiceNum);
                } 
                $response = [
                    'message' => Lang::get('lang.usage') . ' ' . Lang::get('lang.successfully_saved'),
                    'id' => $insert
                ];               
            }
            return response()->json($response, 201);
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];

        return response()->json($response, 404);
    }

    public function inventoryItemStore(Request $request)
    {
        $validator = Validator::make($request->all(), [ 
            "invoice_id_fk"     => "required",
            "item_id_fk"        => "required",
            "inventory_type"    => "required",
            "type"              => "required",
            "quantity"          => "required",
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }
        if($request->type == 'receiving'){
            $validator = Validator::make($request->all(), [ 
                "price"         => "required",
                "amount"        => "required"
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), 404);
            }
        }
        if($request->type == 'sales') {
            $validator = Validator::make($request->all(), [
                "user"          => "required",
                "remarks"       => "required_if:inventory_type,Wastage"
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), 404);
            }
        }
        
        $array = array(
            "table"             => 'inventory_invoice_items',
            "fields"            => array('*'),
            "where"             => ["invoice_id_fk" => $request->invoice_id_fk,"item_id_fk" => $request->item_id_fk],
        );
        if(CommonModel::get_row($array)){
            $response['message'] = (($request->inventory_type == 'inventory') ? Lang::get('lang.inventory')  : Lang::get('lang.usage')) . ' ' . Lang::get('lang.item') .' already exists!';
            return response()->json($response, 201);
        }
        
        
        $array = array(
                    'invoice_id_fk'     => $request->invoice_id_fk,
                    'item_id_fk'        => $request->item_id_fk,
                    "inventory_type"    => $request->inventory_type,
                    "type"              => $request->type,
                    "quantity"          => $request->quantity,                    
                    "created_by"        => Auth::user()->id
                );
        if($request->inventory_type == 'inventory') {
            $array += ["price" => $request->price, "amount" => $request->amount];
        } else {
            $array["user_id_fk"]    = $request->user;
            $array["remarks"]       = $request->remarks;
        }
        $array = array(
            "table"    => "inventory_invoice_items",
            "data"     => $array
        );

        if(CommonModel::insert_data($array)) {
            $response['message'] = (($request->inventory_type == 'inventory') ? Lang::get('lang.inventory') :  Lang::get('lang.usage')). ' ' . Lang::get('lang.item'). ' ' . Lang::get('lang.successfully_saved');
            return response()->json($response, 201);
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];

        return response()->json($response, 404);
    }

    public function getInventoryItemData(Request $request)
    {
        $limit = 0;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $offset = $request->rowOffset;
        $where = array();
        if(isset($request->invoice_id_fk)) $where["invoice_id_fk"] = $request->invoice_id_fk;

        $leftJoinArray = [['table' => 'inventory_items_list as item', 'left_side' => 'item.id', 'right_side' => 'invoice.item_id_fk'],['table' => 'inventory_units_list as unit', 'left_side' => 'unit.id', 'right_side' => 'item.unit_id_fk']];

        $fields = ['invoice.id','item.id as item_id','item.item_name','item.unit_id_fk',DB::raw('ABS(invoice.quantity) as quantity'),'invoice.price','invoice.amount','unit.unit_name','unit.short_name'];
        
        $req_param = array(
            "fields"        => $fields,
            "table"         => 'inventory_invoice_items as invoice',
            "left_join"     => $leftJoinArray,
            "where"         => $where
        );
        if(isset($request->type) && $request->type == 'sales') {
            $additionalLeftJoin = ['table' => 'users', 'left_side' => 'users.id', 'right_side' => 'invoice.user_id_fk'];
            $leftJoinArray[] = $additionalLeftJoin;
            $fields = array_merge($fields,array('users.id as us_id','invoice.remarks'));
            $fields_raw = 'CONCAT(users.first_name," ",users.last_name) AS us_name';
            $req_param = array_merge($req_param, array("fields_raw" => $fields_raw, "fields" => $fields, "left_join"     => $leftJoinArray));
        }

        if($limit != 0) $req_param = array_merge($req_param, array("limit" => array('offset' => $offset,'limit' => $limit)));
        $list = CommonModel::get_data($req_param);
        return $list;        
    }

    public function getInventoryItemUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            "id"                => "required", 
            "invoice_id_fk"     => "required",
            "item_id_fk"        => "required",
            "inventory_type"    => "required",
            "type"              => "required",
            "quantity"          => "required",
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }
        if($request->type == 'receiving'){
            $validator = Validator::make($request->all(), [ 
                "price"          => "required",
                "amount"         => "required"
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), 404);
            }
        }
        if($request->type == 'sales') {
            $validator = Validator::make($request->all(), [ 
                "user"          => "required",
                "remarks"       => "required_if:inventory_type,Wastage"
            ]);
            if ($validator->fails()) {
                return response()->json($validator->errors(), 404);
            }
        }

        $array = array(
                    "table"   => 'inventory_invoice_items',
                    "fields"  => array('*'),
                    "where"   => array("invoice_id_fk" => $request->invoice_id_fk,"item_id_fk" => $request->item_id_fk),
                );
        $row_id = CommonModel::get_row($array);        
        if(isset($row_id) && $request->id != $row_id->id){
            $response = [
                'message' => Lang::get('lang.inventory') . ' '.Lang::get('lang.item') . ' already exists!'
            ];
            return response()->json($response, 201);
        }

        $data = array(
                    'invoice_id_fk'     => $request->invoice_id_fk,
                    'item_id_fk'        => $request->item_id_fk,
                    "inventory_type"    => $request->inventory_type,
                    "type"              => $request->type,
                    "quantity"          => $request->quantity,
                    "remarks"           => $request->remarks,
                    "updated_at"        => date('Y-m-d H:i:s')
                );
        if($request->inventory_type == 'inventory') {
            $data = array_merge($data, array("price" => $request->price, "amount" => $request->amount));
        } else {
            $data = array_merge($data, array("user_id_fk" => $request->user));
        }

        $array = array(
            "table"    => "inventory_invoice_items",
            "data"     => $data,
            "where"    => array("id" => $request->id),
        );        
        if(CommonModel::update_data($array)) {
            $response['message'] = (($request->type == 'receiving') ? Lang::get('lang.inventory') :  Lang::get('lang.usage')). ' ' . Lang::get('lang.item'). ' ' . Lang::get('lang.successfully_updated');
            return response()->json($response, 201);
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];

        return response()->json($response, 404);
    }

    public function inventoryDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [ 
            "id"    => "required",
            "type"  => "required",
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }
     

        $status = ($request->type == 'receiving') ? Lang::get('lang.inventory') : Lang::get('lang.usage');
      

        $array = array(
            "table"             => 'inventory_invoice',
            "fields"            => array('*'),
            "where"             => ["id" => $request->id,"status" => "Draft"],
        );
        $checkData = CommonModel::get_row($array);

        if(!empty($checkData)){
            $array = array(
                "table"             => "inventory_invoice_items",
                "where"             => array("invoice_id_fk" => $request->id),
            );
            CommonModel::delete_data($array);
         
           
            $array = array(
                "table"             => "inventory_invoice",
                "where"             => array("id" => $request->id),
            );
            CommonModel::delete_data($array);
            $response = [
                'message' => $status . ' ' . Lang::get('lang.successfully_deleted')
            ];
            return response()->json($response, 200);
                     
        } else {            
            $response = [
                'message' => $status.' '.Lang::get('lang.in_use').', '.Lang::get('lang.you_can_not_delete_the').' '.strtolower($status)
            ];
            return response()->json($response, 201);
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];
      
        return response()->json($response, 404);
    }

    public function inventoryImageDelete($id, $img)
    {
        $array = array(
            "table"             => 'inventory_invoice',
            "fields"            => array('*'),
            "where"             => ["id" => $id,"status" => "Draft"],
        );
        $result = CommonModel::get_row($array);
        if($result){
            $filePath = "uploads/inventory_bills/".$img;
            $imgs = implode(",", array_diff(explode(",", $result->bills), array($img)));
            $imgs = (!empty($imgs) || $imgs != '') ? $imgs : null;
            $array = array(
                "table"             => "inventory_invoice",
                "where"             => array("id" => $id),
                "data"              => array('bills' => $imgs),
            );
            if(CommonModel::update_data($array)) {
                (file_exists($filePath)) ? unlink($filePath) : '';
                $response = [
                    'message' => Lang::get('lang.item_image') . ' ' . Lang::get('lang.successfully_deleted')
                ];
                return response()->json($response, 200);
            }            
        } else {            
            $response = [
                'message' => Lang::get('lang.item_image').' '.Lang::get('lang.in_use').', '.Lang::get('lang.you_can_not_delete_the').' '.strtolower(Lang::get('lang.item_image'))
            ];
            return response()->json($response, 201);
        }

        $response = [
            'message' => Lang::get('lang.getting_problems')
        ];
        return response()->json($response, 404);
    }

    public function inventoryUpdate(Request $request)
    {
        $validator = Validator::make($request->all(), [ 
            "id"    => "required",
            "type"  => "required",
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }

        $array = array(
            "table"             => 'inventory_invoice_items',
            "fields"            => array('*'),
            "where"             => ["invoice_id_fk" => $request->id],
        );
        
        if(!CommonModel::get_row($array)){
            $response['message'] = ucfirst(strtolower('Add atleast one '. (($request->type == 'receiving') ? Lang::get('lang.inventory') :  Lang::get('lang.usage')). ' ' . Lang::get('lang.item').'!'));
            return response()->json($response, 201);
        }

        $array = array(
            "table"    => "inventory_invoice",
            "data"     => ['status' => 'Completed'],
            "where"    => array("id" => $request->id),
        );        
        if(CommonModel::update_data($array)) {
            $response['message'] = ucfirst(strtolower((($request->type == 'receiving') ? Lang::get('lang.inventory') : Lang::get('lang.usage')). ' ' . Lang::get('lang.successfully_saved')));
            return response()->json($response, 201);
        }

        $response = ['message' => Lang::get('lang.getting_problems')];
        return response()->json($response, 404);
    }

    public function getSupportingData(Request $request)
    {
        $invoice = $usage = $order = [];
        if($request->status == "completed") {
            $invoice = CommonModel::get_data(["table"=>'inventory_invoice',"fields"=>['invoice_id as text','id as value'],"where" => ["type" => "receiving","status" => "Completed"]]);
            $order = CommonModel::get_data(["table"=>'inventory_invoice',"fields"=> ['order_id as text','id as value'],"where" => ["type" => "receiving","status" => "Completed"]]);
        }
        if($request->status == "receiving") {
            $invoice = CommonModel::get_data(["table"=>'inventory_invoice',"fields"=>['invoice_id as text','id as value'],"where" => ["type" => "receiving"]]);
            $order = CommonModel::get_data(["table"=>'inventory_invoice',"fields"=> ['order_id as text','id as value']]);
        }
        if($request->status == "sales") {
            $usage = CommonModel::get_data(["table"=>'inventory_invoice',"fields"=>['invoice_id as text','id as value'],"where" => ["type" => "sales"]]);
        }
        $suppliers = Supplier::index([DB::raw('CONCAT(first_name," ", last_name) as text'), 'id as value']);
        $users = CommonModel::get_data(["table"=>'inventory_sales_users',"fields"=>[DB::raw('CONCAT(first_name," ", last_name) as text'), 'id as value'], "where" => ["status" => "1"]]);
        $items = CommonModel::get_data(["table"=>'inventory_items_list',"fields"=>['item_name as text','id as value']]);


        return ['suppliers' => $suppliers,'order' => (!empty($order)) ? $order['datarows'] : [],'invoice' => (!empty($invoice)) ? $invoice['datarows'] : [],'users' => $users['datarows'],'usage' => (!empty($usage)) ? $usage['datarows'] : [],'items' => $items['datarows']];
    }

    public function getUsersList(Request $request)
    {
        $limit = 0;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $offset = $request->rowOffset;
        $where = array();
        if(isset($request->invoice_id_fk)) $where = array_merge($where,array("invoice_id_fk" => $request->invoice_id_fk));

        $_where = $where;
        $_where['status'] = '1';

        $req_param = array(
            "fields"        => array('inventory_sales_users.id'),
            "fields_raw"    => 'CONCAT(inventory_sales_users.first_name," ",inventory_sales_users.last_name) AS name',
            "table"         => 'inventory_sales_users',
            "where"         => $_where
        );
        if($limit != 0) $req_param = array_merge($req_param, array("limit" => array('offset' => $offset,'limit' => $limit)));
        $list = CommonModel::get_data($req_param);
 
        return $list;
    }

    public function getItemQuantity(Request $request)
    {
        $validator = Validator::make($request->all(), [ 
            "id"                => "required"
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }

        $leftJoinArray = [['table' => 'inventory_invoice as invoice', 'left_side' => 'invoice.id', 'right_side' => 'item.invoice_id_fk']];

        $array = array(
            "table"             => 'inventory_invoice_items as item',
            "fields_raw"        => 'COALESCE(SUM(item.quantity),0) AS quantity',
            "left_join"         => $leftJoinArray,
            "where"             => array("item_id_fk" => $request->id,"status" => "Completed"),
        );
        $row = CommonModel::get_row($array);
        if(empty($row->quantity)){
            $row->quantity = 0;
        }
        return $row;
    }

    public function getItemsQuantity(Request $request)
    {
        $array = array(
            "fields"            => array('item.*'),
            "fields_raw"        => 'CONCAT(COALESCE(SUM(`invoice`.`quantity`), 0), " ", COALESCE(`unit`.`short_name`, "")) AS quantity',
            "table"             => 'inventory_items_list as item',
            "left_join"         => [['table' => 'inventory_invoice_items as invoice', 'left_side' => 'invoice.item_id_fk', 'right_side' => 'item.id'],['table' => 'inventory_units_list as unit', 'left_side' => 'unit.id', 'right_side' => 'item.unit_id_fk']],
            "groupby"           => 'item.id'
        );
        if(isset($request->searchValue)) {
            $search_columns_arr = ['item.item_name'];
            $array['search'] = array(
                "search_columns_arr" => $search_columns_arr,
                "search_string" => $request->searchValue
            );
        }
         $limit = 0;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $offset = $request->rowOffset;
        if($limit != 0) $array = array_merge($array, array("limit" => array('offset' => $offset,'limit' => $limit)));
        $data = CommonModel::get_data($array);
        return $data;
    }

    public function inventoryFilesStore(Request $request)
    {

        $validator = Validator::make($request->all(), [ 
            "id"                => "required",
            // "billsFiles"        => "required"
        ]);
        if ($validator->fails()) {
            return response()->json($validator->errors(), 404);
        }

        $array = array(
            "table"             => 'inventory_invoice',
            "fields"            => array('*'),
            "where"             => array("id" => $request->id),
        );
        $row = CommonModel::get_row($array);
        if(!$row){
            $response = [
                'message' => Lang::get('lang.inventory') . ' record not found!'
            ];
            return response()->json($response, 201);
        }

        $filePath = '';
        if($row->bills){
            $filePath = $row->bills;
        }
        $imageHandler = new imageHandler;
        $files = (array)$request->billsFiles;
        
        foreach ($files as $key => $value) {
            $filePath .= ($filePath != '') ? ',' : '';
            $filePath .= $imageHandler->imageUpload($value, 'IN_', 'uploads/inventory_bills/');
        }
        if($filePath != ''){
            $array = array(
                "table"    => "inventory_invoice",
                "data"     => array('bills' => $filePath),
                "where"    => array("id" => $request->id),
            );
            
            if(CommonModel::update_data($array)) {
                $response = [
                    'message' => Lang::get('lang.files_uploaded')
                ];
                return response()->json($response, 201);
            }

            $response = [
                'message' => Lang::get('lang.getting_problems')
            ];
            return response()->json($response, 404);
        } else{
            $response = [
                'message' => Lang::get('lang.getting_problems')
            ];
            return response()->json($response, 404);
        }        
    }

    public function inventoryExportAll(Request $request)
    {
        $data = [];$i = 1;
        if($request->type == 'usage') {
            $availableStock = $this->inventoryReport($request);    
        }
        if($request->type == 'all') {
            $availableStock = $this->getItemsQuantity($request);            
        }
        if($availableStock['datarows'] && count($availableStock['datarows']) > 0){
            foreach ($availableStock['datarows'] as $key => $value) {
                $data[] = array(
                    's_no'              =>  $i,
                    'item_name'         =>  $value->item_name,
                    'quantity'          =>  $value->quantity,
                );
                $i++;
            }
        }
        $headings[] = ["S.No", "Item Name", "Quantity"];
        return Excel::download(new ExportData($data,$headings), 'central-store.xlsx');
    }

    public function inventoryReport(Request $request)
    {
        $where = array('invoice.status' => 'Completed', 'invoice.type' => $request->type);
        $where_bt = array();
        if(isset($request->filtersData)) {
            $filtersData = $request->filtersData;
            foreach ($filtersData as $singleFilter) {
                if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                    $where_bt = ["column" => "invoice.date",
                                          "from_date" => $singleFilter['value'][0]['start'],
                                          "to_date" => $singleFilter['value'][0]['end']
                                        ];
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "inventory_type") {
                    $where = array_merge($where,["invoice.inventory_type" => $singleFilter['value']]);
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "item_name") {
                    $where = array_merge($where,["item.id" => $singleFilter['value']]);
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "supplier") {
                    $where = array_merge($where,["invoice.supplier_id_fk" => $singleFilter['value']]);
                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "user_name") {
                    $where = array_merge($where,["users.id" => $singleFilter['value']]);
                }
            }
        } 

        if($request->checkType == 'download') {
            if(isset($request->user_name)) {
                $where = array_merge($where,["users.id" => $request->user_name]);
            }
            if(isset($request->supplier)) {
                $where = array_merge($where,["invoice.supplier_id_fk" => $request->supplier]);
            }
            if(isset($request->item_name)) {
                $where = array_merge($where,["item.id" => $request->item_name]);
            }
            if(isset($request->inventory_type)) {
                $where = array_merge($where,["invoice.inventory_type" => $request->inventory_type]);
            }
            if(isset($request->date_range)) {
                $dt = explode(" - ", $request->date_range);
                $where_bt = [   "column" => "invoice.date",
                                "from_date" => date("Y-m-d", strtotime($dt[0])),
                                "to_date" => date("Y-m-d", strtotime($dt[1]))
                            ];
            }
        }

        $leftJoinArray = [['table' => 'inventory_invoice_items as in_items', 'left_side' => 'in_items.item_id_fk', 'right_side' => 'item.id'],['table' => 'inventory_invoice as invoice', 'left_side' => 'invoice.id', 'right_side' => 'in_items.invoice_id_fk'],['table' => 'inventory_units_list as unit', 'left_side' => 'unit.id', 'right_side' => 'item.unit_id_fk']];
        $fields = ['invoice.id as in_id','invoice.date',DB::raw('CASE WHEN invoice.inventory_type = "Wastage" THEN "Damage / Expired" ELSE invoice.inventory_type END AS inventory_type'),'invoice.invoice_id','item.id','item.item_name','in_items.quantity'];
        $fields_raw = 'CONCAT( DATE_FORMAT(invoice.date, "%d/%m/%Y"),  " ",  DATE_FORMAT(invoice.time, "%h:%i"), " ", CASE WHEN DATE_FORMAT(invoice.time, "%p") = "AM" THEN "am" ELSE "pm" END ) AS date_time,
        CONCAT(COALESCE(ABS(`in_items`.`quantity`), 0), " ", COALESCE(`unit`.`short_name`, "")) AS quantity';
        if($request->type == 'sales') {

            $leftJoinArray[] = ['table' => 'users', 'left_side' => 'users.id', 'right_side' => 'invoice.user_id_fk'];
            $fields_raw .= ',CONCAT(users.first_name," ",users.last_name) AS us_name';
            $fields[] = 'invoice.time';
            $fields[] = 'invoice.user_id_fk';

        } else if($request->type == 'receiving') {

            $leftJoinArray[] = ['table' => 'suppliers', 'left_side' => 'suppliers.id', 'right_side' => 'invoice.supplier_id_fk'];
            $fields[] = 'suppliers.id';
            $fields[] = 'in_items.price';
            $fields[] = 'in_items.amount';
            $fields[] = DB::raw('DATE_FORMAT(invoice.date, "%d/%m/%Y") as date1');
            $fields_raw .= ',CONCAT(suppliers.first_name," ",suppliers.last_name) AS name';

        }
 
        $array = [
            "fields"            => $fields,
            "fields_raw"        => $fields_raw,
            "table"             => 'inventory_items_list as item',
            "left_join"         => $leftJoinArray,
            "where"             => $where,
            "where_bt"          => $where_bt,
            "order_by"          => ["column" => "invoice.id", "direction" => "DESC"]
        ];

        if(isset($request->searchValue)) {
            if($request->type == 'sales') {
                $search_col = ['item.item_name','users.first_name', 'users.last_name','invoice.inventory_type'];
            } else if($request->type == 'receiving') {
                $search_col = ['item.item_name','suppliers.first_name', 'suppliers.last_name', 'invoice.invoice_id'];
            }
            $array['search'] = array(
                "search_columns_arr" => $search_col,
                "search_string" => $request->searchValue
            );
        }

        $limit = 0;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $offset = $request->rowOffset;
        if($limit != 0) $array = array_merge($array, array("limit" => array('offset' => $offset,'limit' => $limit)));
        $data = CommonModel::get_data($array);
     
        if($request->checkType == 'download') {
            $excelData = [];$i = 1;
            if($request->type == 'sales') {
                if($data['datarows'] && count($data['datarows']) > 0){
                    foreach ($data['datarows'] as $key => $value) {
                        $excelData[] = array(
                            's_no'              =>  $i,
                            'date_time'         =>  $value->date_time,
                            'inventory_type'    =>  $value->inventory_type,
                            'item_name'         =>  $value->item_name,
                            'us_name'           =>  $value->us_name,
                            'quantity'          =>  $value->quantity,
                            
                        );
                        $i++;
                    }
                }
                $headings[] = ["S.No", Lang::get('lang.date_time'), Lang::get('lang.type'), Lang::get('lang.item_name'), Lang::get('lang.user_name'), Lang::get('lang.usage_qty')];
                return Excel::download(new ExportData($excelData,$headings), 'usage-details.xlsx');
            } 
            else if($request->type == 'receiving') {

                if($data['datarows'] && count($data['datarows']) > 0){
                    foreach ($data['datarows'] as $key => $value) {
                        $excelData[] = array(
                            's_no'              =>  $i,
                            'invoice_id'        =>  $value->invoice_id,
                            'date'              =>  $value->date1,
                            'supplier_name'     =>  $value->name,
                            'item_name'         =>  $value->item_name,                            
                            'quantity'          =>  $value->quantity,
                            'unit_price'        =>  $value->price,
                            'cost_price'        =>  $value->amount,
                        );
                        $i++;
                    }
                }
                $headings[] = ["S.No", Lang::get('lang.invoice_id'), Lang::get('lang.date'),Lang::get('lang.supplier_name'), Lang::get('lang.item_name'),  Lang::get('lang.quantity'), Lang::get('lang.unit_price'), Lang::get('lang.cost_price')];
                return Excel::download(new ExportData($excelData,$headings), 'purchases-details.xlsx');
            }
        }
        return $data;
    }

    public function itemsWiseReport(Request $request)
    {
        $where = array('invoice.status' => 'Completed', 'invoice.type' => 'receiving');
        $where_bt = array();


        $fields = ['invoice.id as in_id','invoice.date','invoice.inventory_type','invoice.invoice_id','item.id','item.item_name','in_items.quantity'];
        $fields_raw = '';

        $leftJoinArray = [['table' => 'inventory_invoice_items as in_items', 'left_side' => 'in_items.item_id_fk', 'right_side' => 'item.id'],['table' => 'inventory_invoice as invoice', 'left_side' => 'invoice.id', 'right_side' => 'in_items.invoice_id_fk'],['table' => 'inventory_units_list as unit', 'left_side' => 'unit.id', 'right_side' => 'item.unit_id_fk']];

        $array = [
            "fields"            => $fields,
            "fields_raw"        => $fields_raw,
            "table"             => 'inventory_items_list as item',
            "left_join"         => $leftJoinArray,
            "where"             => $where,
            "where_bt"          => $where_bt
        ];

        if(isset($request->searchValue)) {
            $search_columns_arr = ['item.item_name'];
            $array['search'] = array(
                "search_columns_arr" => $search_columns_arr,
                "search_string" => $request->searchValue
            );
        }

        $limit = 0;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $offset = $request->rowOffset;
        if($limit != 0) $array = array_merge($array, array("limit" => array('offset' => $offset,'limit' => $limit)));
        $data = CommonModel::get_data($array);

        return $data;
    }
}