<?php

namespace App\Exports;

use App\Http\Controllers\API\PermissionController;
use App\Models\OrderItems;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class StockAdjustmentExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {
        $perm = new PermissionController();
        $permission = $perm->checkSalesPermission();

        $query = OrderItems::query()->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->leftJoin('products', 'products.id', '=', 'order_items.product_id')
            ->leftJoin('product_variants', 'product_variants.id', '=', 'order_items.variant_id')
            ->join('users', 'users.id', '=', 'orders.created_by')
            ->leftJoin('branches', 'branches.id', 'orders.branch_id')
            ->join('adjust_product_stock_types', 'adjust_product_stock_types.id', 'order_items.adjust_stock_type_id')
            ->select(
                'orders.invoice_id as invoice_id',
                'orders.date as date',
                'products.title as product_name',
                'product_variants.variant_title as variant_title',
                'branches.name as branch_name',
                'order_items.quantity as adjustment_item',
                'adjust_product_stock_types.title as adjustment_type',
                DB::raw('sum(order_items.quantity) as adjustment_item'),
                DB::raw("CONCAT(users.first_name,' ',users.last_name)  AS created_by")
            )
            ->where('orders.order_type', '=', 'adjustment')
            ->where('orders.status', '=', 'done')
            ->groupBy('order_items.order_id')
            ->groupBy('order_items.product_id')
            ->groupBy('orders.branch_id')
            ->groupBy('order_items.variant_id')
            ->groupBy('order_items.adjust_stock_type_id');

        if ($permission == 'personal') {
            $query->where('orders.created_by', Auth::user()->id);
        }

        return $query->orderBy('invoice_id', 'ASC');
    }

    public function map($reportRow): array
    {
        return
            [
                $reportRow->invoice_id,
                $reportRow->date,
                $reportRow->product_name,
                $reportRow->variant_title,
                $reportRow->adjustment_item,
                $reportRow->branch_name,
                $reportRow->adjustment_type,
                $reportRow->created_by,
            ];
    }

    public function headings(): array
    {
        return [
            Lang::get('lang.invoice_id'),
            Lang::get('lang.date'),
            Lang::get('lang.product_name'),
            Lang::get('lang.variant_title'),
            Lang::get('lang.adjustment_item'),
            Lang::get('lang.branch_name'),
            Lang::get('lang.adjustment_type'),
            Lang::get('lang.sold_by'),
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:H1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );


                // $query_result = $this->query()->get();
                // $rows_count = count($query_result);

                // $item_purchase = $query_result->sum('item_purchased');
                // $tax = $query_result->sum('tax');
                // $discount = $query_result->sum('discount');
                // $total = $query_result->sum('total');
                // $due_amount = $query_result->sum('due_amount');

                // $rows_count = $rows_count + 2;

                // $event->sheet->setCellValue('A' . $rows_count, 'Total');
                // $event->sheet->setCellValue('F' . $rows_count, number_format($item_purchase, 2));
                // $event->sheet->setCellValue('G' . $rows_count, number_format($tax, 2));
                // $event->sheet->setCellValue('H' . $rows_count, number_format($discount, 2));
                // $event->sheet->setCellValue('I' . $rows_count, number_format($total, 2));
                // $event->sheet->setCellValue('J' . $rows_count, number_format($due_amount, 2));

            },
        ];
    }

}
